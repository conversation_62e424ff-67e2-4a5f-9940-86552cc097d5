# 密码分析工具配置文件
# ========================

# 分析器配置
analyzer:
  # 最大并发工作线程数
  max_workers: 4
  
  # 分析超时时间（秒）
  timeout: 30
  
  # 最小置信度阈值
  min_confidence: 0.1
  
  # 最大尝试次数
  max_attempts: 50

# 日志配置
logging:
  # 日志级别: DEBUG, INFO, WARNING, ERROR
  level: INFO
  
  # 日志文件路径
  file: "logs/crypto_analyzer.log"
  
  # 是否输出到控制台
  console: true
  
  # 日志格式
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# 检测器配置
detectors:
  # 编码算法检测器
  encoding:
    base64:
      enabled: true
      priority: 90
    base32:
      enabled: true
      priority: 85
    base16:
      enabled: true
      priority: 80
    url_encoding:
      enabled: true
      priority: 75
    html_entities:
      enabled: true
      priority: 70
    rot13:
      enabled: true
      priority: 65
    binary:
      enabled: true
      priority: 60
  
  # 对称加密算法检测器
  symmetric:
    aes:
      enabled: true
      priority: 85
      # 尝试的模式
      modes: ["CBC", "ECB", "CTR", "GCM"]
      # 常见密钥长度
      key_lengths: [16, 24, 32]
    des:
      enabled: true
      priority: 70
    triple_des:
      enabled: true
      priority: 75
    blowfish:
      enabled: true
      priority: 65
  
  # 非对称加密算法检测器
  asymmetric:
    rsa:
      enabled: true
      priority: 80
  
  # 哈希算法检测器
  hash:
    md5:
      enabled: true
      priority: 95
    sha1:
      enabled: true
      priority: 90
    sha256:
      enabled: true
      priority: 85
    sha512:
      enabled: true
      priority: 80
  
  # 古典密码检测器
  classical:
    caesar:
      enabled: true
      priority: 70
    vigenere:
      enabled: true
      priority: 65
    rail_fence:
      enabled: true
      priority: 60
    morse_code:
      enabled: true
      priority: 55

# 置信度评分配置
confidence:
  # 各项评分权重
  weights:
    algorithm_match: 0.25
    readability: 0.35
    char_distribution: 0.20
    semantic_analysis: 0.20
  
  # 可读性评分参数
  readability:
    min_printable_ratio: 0.8
    min_ascii_ratio: 0.7
  
  # 字符分布评分参数
  char_distribution:
    max_chi_squared: 1000

# 输出配置
output:
  # 默认输出格式: json, yaml, text
  format: json
  
  # 是否包含详细信息
  verbose: true
  
  # 是否显示失败的尝试
  show_failures: false
  
  # 最大显示结果数
  max_results: 10

# 性能配置
performance:
  # 是否启用缓存
  enable_cache: true
  
  # 缓存大小
  cache_size: 1000
  
  # 是否启用性能监控
  enable_profiling: false
