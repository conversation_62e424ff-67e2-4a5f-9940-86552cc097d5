#!/usr/bin/env python3
"""
分析加密串脚本
==============

分析用户提供的加密数据。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crypto_analyzer import CryptoAnalyzer


def analyze_encrypted_data():
    """分析加密数据"""
    
    # 读取加密数据
    with open('加密串.txt', 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 分割两个加密串
    parts = content.split('和')
    
    if len(parts) != 2:
        print("❌ 数据格式不正确，应该包含两个用'和'分隔的加密串")
        return
    
    encrypted_data1 = parts[0].strip()
    encrypted_data2 = parts[1].strip()
    
    print("🔍 密码分析工具 - 加密串分析")
    print("=" * 60)
    
    # 创建分析器
    analyzer = CryptoAnalyzer()
    
    # 分析第一个加密串
    print(f"\n📊 分析第一个加密串 (长度: {len(encrypted_data1)}):")
    print(f"数据预览: {encrypted_data1[:100]}...")
    
    result1 = analyzer.analyze(encrypted_data1)
    print_analysis_result(result1, "第一个加密串")
    
    # 分析第二个加密串
    print(f"\n📊 分析第二个加密串 (长度: {len(encrypted_data2)}):")
    print(f"数据预览: {encrypted_data2[:100]}...")
    
    result2 = analyzer.analyze(encrypted_data2)
    print_analysis_result(result2, "第二个加密串")
    
    # 综合分析
    print(f"\n🎯 综合分析结论:")
    print("=" * 40)
    
    if result1.best_result and result2.best_result:
        print(f"✅ 两个加密串都成功解密")
        print(f"第一个串最佳算法: {result1.best_result.algorithm} (置信度: {result1.best_result.confidence:.2%})")
        print(f"第二个串最佳算法: {result2.best_result.algorithm} (置信度: {result2.best_result.confidence:.2%})")
        
        # 检查是否是相同的算法
        if result1.best_result.algorithm == result2.best_result.algorithm:
            print(f"🔗 两个串使用相同的算法: {result1.best_result.algorithm}")
        
    elif result1.best_result or result2.best_result:
        print(f"⚠️ 只有一个加密串成功解密")
        if result1.best_result:
            print(f"第一个串: {result1.best_result.algorithm} (置信度: {result1.best_result.confidence:.2%})")
        if result2.best_result:
            print(f"第二个串: {result2.best_result.algorithm} (置信度: {result2.best_result.confidence:.2%})")
    else:
        print(f"❌ 两个加密串都未能成功解密")
        print(f"建议尝试其他方法或提供更多信息（如密钥、算法提示等）")


def print_analysis_result(result, title):
    """打印分析结果"""
    print(f"\n--- {title} 分析结果 ---")
    print(f"分析时间: {result.analysis_time:.3f}秒")
    print(f"检测到的算法: {', '.join(result.detected_algorithms[:5])}")
    print(f"尝试次数: {len(result.attempts)}")
    print(f"成功次数: {len(result.get_successful_attempts())}")
    
    if result.best_result:
        print(f"\n🎯 最佳结果:")
        print(f"  算法: {result.best_result.algorithm}")
        print(f"  置信度: {result.best_result.confidence:.2%}")
        if result.best_result.key:
            print(f"  密钥: {result.best_result.key}")
        if result.best_result.mode:
            print(f"  模式: {result.best_result.mode}")
        
        # 显示解密结果
        if result.best_result.result:
            if len(result.best_result.result) > 200:
                print(f"  解密结果: {result.best_result.result[:200]}...")
            else:
                print(f"  解密结果: {result.best_result.result}")
        
        # 显示其他成功的尝试
        successful = result.get_successful_attempts()
        if len(successful) > 1:
            print(f"\n📋 其他成功的尝试:")
            for attempt in successful[1:3]:  # 显示前3个
                print(f"  - {attempt.algorithm}: {attempt.confidence:.2%}")
    else:
        print(f"\n❌ 未找到有效的解密结果")
        
        # 显示检测到的算法但解密失败的情况
        if result.detected_algorithms:
            print(f"检测到可能的算法但解密失败: {', '.join(result.detected_algorithms[:3])}")


if __name__ == '__main__':
    try:
        analyze_encrypted_data()
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()
