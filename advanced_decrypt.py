#!/usr/bin/env python3
"""
高级解密分析
============

对解密后的内容进行进一步分析和处理。
"""

import base64
import binascii
import zlib
import gzip
import bz2
import lzma
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

def advanced_analysis():
    """高级解密分析"""
    
    # 原始加密串
    first_string = "yX67CAY2RrMSJ1TxneWBANxXBK5wL6Rvk2bDRa+DKUspUee9v69x1s3TH1rv8tP4wG6sJnnrJmZfMM7TEqmnisgTL6BdEeeLi7oyL6S3MwvEjGQlqs0iPesluloSgPHLd9fQCwbcvjzG10olmLTRox7llp7QHyQ1I4Jj76o4+Ldetv9hUFILGWA9t2d6cLdAlVuymsfY95N9rBQYn7gyHMzGp8h6Xx7dIPQzOUoq6F++fSQfNbp7EcW8WC6BGaKpUxBRQS1J5AG2G5BqhbnedXY4w7GogDKI1hALiTgcsoF8NHy7/3gwZ8o4AX6CvvXPmaefqio3cQZMiNcK9P0sU8l5NAwEuUhsnO4df+JrAV9vY0O3uw6zgrRDaDZvjm0elQKTpXQtWJzN8DolK51ddhELbxpZx7u6rF3SoX1DlPJOpXvoqe2zmV4YzD6O3QG9UQU4Nc4FmX8hNAEm8XRuavVaK6wWX17I4jRA5eG9zrNvl0SybAS6PbPGqCfp04uIcKhpFDPrALGzdy/yWi/2GKXSmGbrxbIFJ2v4DYPX0Gw="
    
    second_string = "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"
    
    print("🔍 高级解密分析")
    print("=" * 60)
    
    # 分析第一个串
    print("\n📊 第一个加密串高级分析:")
    analyze_string_advanced(first_string, "第一个串")
    
    # 分析第二个串
    print("\n📊 第二个加密串高级分析:")
    analyze_string_advanced(second_string, "第二个串")

def analyze_string_advanced(encrypted_string, label):
    """对单个加密串进行高级分析"""
    
    print(f"\n--- {label} ---")
    
    # Base64解码
    try:
        decoded_data = base64.b64decode(encrypted_string)
        print(f"Base64解码成功: {len(decoded_data)} 字节")
        
        # 尝试最有希望的密钥进行AES解密
        best_keys = [
            b"1234567890123456",
            b"password12345678", 
            b"secretkey1234567"
        ]
        
        for key in best_keys:
            print(f"\n🔑 使用密钥: {key.decode()}")
            
            # 尝试ECB模式
            decrypted_ecb = try_aes_decrypt(decoded_data, key, "ECB")
            if decrypted_ecb:
                print(f"✅ AES-ECB解密成功")
                analyze_decrypted_content(decrypted_ecb, f"{label}-ECB-{key.decode()}")
            
            # 尝试CBC模式
            decrypted_cbc = try_aes_decrypt(decoded_data, key, "CBC")
            if decrypted_cbc:
                print(f"✅ AES-CBC解密成功")
                analyze_decrypted_content(decrypted_cbc, f"{label}-CBC-{key.decode()}")
                
    except Exception as e:
        print(f"❌ Base64解码失败: {e}")

def try_aes_decrypt(ciphertext, key, mode_name):
    """尝试AES解密"""
    try:
        if mode_name == "ECB":
            cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend())
        elif mode_name == "CBC":
            cipher = Cipher(algorithms.AES(key), modes.CBC(b'\x00' * 16), backend=default_backend())
        else:
            return None
            
        decryptor = cipher.decryptor()
        decrypted = decryptor.update(ciphertext) + decryptor.finalize()
        return decrypted
        
    except Exception:
        return None

def analyze_decrypted_content(data, context):
    """分析解密后的内容"""
    
    print(f"\n🔬 分析解密内容 ({context}):")
    print(f"数据长度: {len(data)} 字节")
    
    # 1. 尝试直接解码为文本
    for encoding in ['utf-8', 'gbk', 'latin-1']:
        try:
            text = data.decode(encoding, errors='ignore')
            printable_ratio = sum(1 for c in text if c.isprintable()) / len(text)
            if printable_ratio > 0.8:
                print(f"✅ {encoding}编码文本 (可打印率: {printable_ratio:.2%}):")
                print(f"内容预览: {text[:200]}...")
                
                # 检查是否包含常见格式
                check_common_formats(text)
                return
        except:
            continue
    
    # 2. 检查是否是压缩数据
    print("\n🗜️ 检查压缩格式:")
    check_compression(data)
    
    # 3. 检查是否是另一层Base64
    print("\n🔄 检查嵌套编码:")
    check_nested_encoding(data)
    
    # 4. 检查二进制特征
    print("\n🔢 二进制数据分析:")
    analyze_binary_data(data)

def check_common_formats(text):
    """检查常见格式"""
    
    # 检查JSON
    if text.strip().startswith('{') or text.strip().startswith('['):
        print("🔍 可能是JSON格式")
        try:
            import json
            json.loads(text)
            print("✅ 有效的JSON数据")
        except:
            print("❌ 无效的JSON数据")
    
    # 检查XML
    if text.strip().startswith('<'):
        print("🔍 可能是XML格式")
    
    # 检查Base64模式
    if len(text) % 4 == 0 and all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=' for c in text):
        print("🔍 可能是Base64编码")
        try:
            nested_data = base64.b64decode(text)
            print(f"✅ 嵌套Base64解码成功: {len(nested_data)} 字节")
            analyze_decrypted_content(nested_data, "嵌套Base64")
        except:
            print("❌ 嵌套Base64解码失败")

def check_compression(data):
    """检查压缩格式"""
    
    # 检查gzip
    if data[:2] == b'\x1f\x8b':
        print("🔍 检测到gzip压缩")
        try:
            decompressed = gzip.decompress(data)
            print(f"✅ gzip解压成功: {len(decompressed)} 字节")
            analyze_decrypted_content(decompressed, "gzip解压")
            return
        except:
            print("❌ gzip解压失败")
    
    # 检查zlib
    try:
        decompressed = zlib.decompress(data)
        print(f"✅ zlib解压成功: {len(decompressed)} 字节")
        analyze_decrypted_content(decompressed, "zlib解压")
        return
    except:
        pass
    
    # 检查bz2
    if data[:3] == b'BZh':
        print("🔍 检测到bzip2压缩")
        try:
            decompressed = bz2.decompress(data)
            print(f"✅ bzip2解压成功: {len(decompressed)} 字节")
            analyze_decrypted_content(decompressed, "bzip2解压")
            return
        except:
            print("❌ bzip2解压失败")
    
    print("❌ 未检测到已知压缩格式")

def check_nested_encoding(data):
    """检查嵌套编码"""
    
    # 尝试将二进制数据当作Base64
    try:
        text = data.decode('ascii', errors='ignore')
        if len(text) % 4 == 0 and all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=' for c in text.replace('\n', '').replace('\r', '')):
            nested_data = base64.b64decode(text)
            print(f"✅ 发现嵌套Base64: {len(nested_data)} 字节")
            analyze_decrypted_content(nested_data, "嵌套Base64")
            return
    except:
        pass
    
    # 尝试十六进制解码
    try:
        text = data.decode('ascii', errors='ignore').replace(' ', '').replace('\n', '')
        if len(text) % 2 == 0 and all(c in '0123456789ABCDEFabcdef' for c in text):
            hex_data = binascii.unhexlify(text)
            print(f"✅ 发现十六进制编码: {len(hex_data)} 字节")
            analyze_decrypted_content(hex_data, "十六进制解码")
            return
    except:
        pass
    
    print("❌ 未发现嵌套编码")

def analyze_binary_data(data):
    """分析二进制数据特征"""
    
    # 计算字节分布
    byte_counts = [0] * 256
    for byte in data:
        byte_counts[byte] += 1
    
    # 计算熵
    entropy = 0
    for count in byte_counts:
        if count > 0:
            p = count / len(data)
            entropy -= p * (p.bit_length() - 1)
    
    print(f"数据熵: {entropy:.3f}")
    
    # 检查常见文件头
    if data[:4] == b'PK\x03\x04':
        print("🔍 可能是ZIP文件")
    elif data[:2] == b'BM':
        print("🔍 可能是BMP图像")
    elif data[:4] == b'\x89PNG':
        print("🔍 可能是PNG图像")
    elif data[:2] == b'\xff\xd8':
        print("🔍 可能是JPEG图像")
    elif data[:4] == b'%PDF':
        print("🔍 可能是PDF文件")
    else:
        print("❌ 未识别出文件类型")
    
    # 显示前32字节的十六进制
    print(f"前32字节 (hex): {data[:32].hex()}")

if __name__ == '__main__':
    advanced_analysis()
