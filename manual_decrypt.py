#!/usr/bin/env python3
"""
手动解密脚本
============

尝试手动解密加密串。
"""

import base64
import binascii
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

def try_base64_decode(data):
    """尝试Base64解码"""
    try:
        decoded = base64.b64decode(data)
        print(f"Base64解码成功，长度: {len(decoded)} 字节")
        
        # 尝试转换为文本
        try:
            text = decoded.decode('utf-8')
            print(f"UTF-8解码: {text[:100]}...")
            return decoded, text
        except:
            print("无法直接转换为UTF-8文本")
            return decoded, None
    except Exception as e:
        print(f"Base64解码失败: {e}")
        return None, None

def analyze_first_string():
    """分析第一个加密串"""
    first_string = "yX67CAY2RrMSJ1TxneWBANxXBK5wL6Rvk2bDRa+DKUspUee9v69x1s3TH1rv8tP4wG6sJnnrJmZfMM7TEqmnisgTL6BdEeeLi7oyL6S3MwvEjGQlqs0iPesluloSgPHLd9fQCwbcvjzG10olmLTRox7llp7QHyQ1I4Jj76o4+Ldetv9hUFILGWA9t2d6cLdAlVuymsfY95N9rBQYn7gyHMzGp8h6Xx7dIPQzOUoq6F++fSQfNbp7EcW8WC6BGaKpUxBRQS1J5AG2G5BqhbnedXY4w7GogDKI1hALiTgcsoF8NHy7/3gwZ8o4AX6CvvXPmaefqio3cQZMiNcK9P0sU8l5NAwEuUhsnO4df+JrAV9vY0O3uw6zgrRDaDZvjm0elQKTpXQtWJzN8DolK51ddhELbxpZx7u6rF3SoX1DlPJOpXvoqe2zmV4YzD6O3QG9UQU4Nc4FmX8hNAEm8XRuavVaK6wWX17I4jRA5eG9zrNvl0SybAS6PbPGqCfp04uIcKhpFDPrALGzdy/yWi/2GKXSmGbrxbIFJ2v4DYPX0Gw="
    
    print("=== 分析第一个加密串 ===")
    print(f"长度: {len(first_string)}")
    print(f"预览: {first_string[:50]}...")
    
    # 尝试Base64解码
    decoded_bytes, decoded_text = try_base64_decode(first_string)
    
    if decoded_bytes:
        print(f"\n十六进制数据: {decoded_bytes[:50].hex()}...")
        
        # 检查是否是AES密文特征
        if len(decoded_bytes) % 16 == 0:
            print(f"数据长度是16的倍数 ({len(decoded_bytes)}字节)，可能是AES密文")
        
        # 尝试不同的解码方式
        print("\n尝试其他解码方式:")
        
        # 尝试再次Base64解码
        try:
            if decoded_text:
                second_decode = base64.b64decode(decoded_text)
                print(f"二次Base64解码成功: {len(second_decode)} 字节")
                try:
                    final_text = second_decode.decode('utf-8')
                    print(f"最终文本: {final_text}")
                except:
                    print("二次解码后仍无法转换为文本")
        except:
            pass

def analyze_second_string():
    """分析第二个加密串"""
    second_string = "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"
    
    print("\n=== 分析第二个加密串 ===")
    print(f"长度: {len(second_string)}")
    print(f"预览: {second_string[:50]}...")
    
    # 尝试Base64解码
    decoded_bytes, decoded_text = try_base64_decode(second_string)
    
    if decoded_bytes:
        print(f"\n十六进制数据: {decoded_bytes[:50].hex()}...")
        
        # 检查是否是AES密文特征
        if len(decoded_bytes) % 16 == 0:
            print(f"数据长度是16的倍数 ({len(decoded_bytes)}字节)，很可能是AES密文")
            
            # 尝试常见的AES密钥
            common_keys = [
                b"1234567890123456",  # 16字节
                b"password12345678",  # 16字节
                b"secretkey1234567",  # 16字节
                b"0123456789abcdef",  # 16字节
            ]
            
            for key in common_keys:
                try_aes_decrypt(decoded_bytes, key)

def try_aes_decrypt(ciphertext, key):
    """尝试AES解密"""
    try:
        # 尝试ECB模式
        cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend())
        decryptor = cipher.decryptor()
        decrypted = decryptor.update(ciphertext) + decryptor.finalize()
        
        try:
            text = decrypted.decode('utf-8', errors='ignore')
            if any(c.isprintable() for c in text):
                print(f"AES-ECB解密成功 (密钥: {key}): {text[:100]}...")
                return text
        except:
            pass
            
        # 尝试CBC模式 (零IV)
        cipher = Cipher(algorithms.AES(key), modes.CBC(b'\x00' * 16), backend=default_backend())
        decryptor = cipher.decryptor()
        decrypted = decryptor.update(ciphertext) + decryptor.finalize()
        
        try:
            text = decrypted.decode('utf-8', errors='ignore')
            if any(c.isprintable() for c in text):
                print(f"AES-CBC解密成功 (密钥: {key}): {text[:100]}...")
                return text
        except:
            pass
            
    except Exception as e:
        pass
    
    return None

if __name__ == '__main__':
    print("🔍 手动解密分析")
    print("=" * 50)
    
    analyze_first_string()
    analyze_second_string()
    
    print("\n" + "=" * 50)
    print("分析完成！")
