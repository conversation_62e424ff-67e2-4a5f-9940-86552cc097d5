# 企业级密码分析工具 🔐

一个功能强大的Python密码分析框架，支持多种加密算法的自动识别和解密。

## ✨ 主要特性

- **🤖 智能检测**: 自动识别可能的加密/编码算法类型
- **🔓 多算法支持**: 支持15+种常见加密和编码算法
- **📊 置信度评分**: 多维度置信度评估系统
- **⚡ 并发处理**: 支持批量处理和并发分析
- **🔧 模块化设计**: 可扩展的插件式架构
- **📋 详细报告**: 生成结构化的分析报告
- **🖥️ 命令行界面**: 友好的CLI工具

## 🎯 支持的算法

### 编码算法
- Base64/Base32/Base16 编码
- URL编码
- HTML实体编码
- ROT13/ROT47
- 二进制编码

### 哈希算法
- MD5
- SHA1/SHA256/SHA512

### 古典密码
- 凯撒密码
- 维吉尼亚密码
- 栅栏密码
- 摩尔斯电码

### 对称加密
- AES (识别，需要密钥解密)
- DES/3DES (计划中)
- Blowfish (计划中)

## 🚀 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 基本使用

#### 1. 命令行分析单个字符串

```bash
python main.py analyze "SGVsbG8gV29ybGQ="
```

#### 2. 批量分析文件

```bash
python main.py batch input.txt -o results.json
```

#### 3. 使用密钥解密

```bash
python main.py analyze "encrypted_data" --key "your_key"
```

#### 4. 查看支持的算法

```bash
python main.py list-algorithms
```

### 编程接口

```python
from crypto_analyzer import CryptoAnalyzer

# 创建分析器
analyzer = CryptoAnalyzer()

# 分析数据
result = analyzer.analyze("SGVsbG8gV29ybGQ=")

# 查看结果
if result.best_result:
    print(f"算法: {result.best_result.algorithm}")
    print(f"置信度: {result.best_result.confidence:.2%}")
    print(f"结果: {result.best_result.result}")
```

## 📁 项目结构

```
crypto_analyzer/
├── core/                   # 核心框架
│   ├── analyzer.py        # 主分析器
│   ├── detector_base.py   # 检测器基类
│   ├── plugin_manager.py  # 插件管理器
│   ├── confidence_scorer.py # 置信度评分器
│   └── result.py          # 结果数据结构
├── detectors/             # 算法检测器
│   ├── encoding/          # 编码算法
│   ├── hash/              # 哈希算法
│   ├── classical/         # 古典密码
│   └── symmetric/         # 对称加密
├── cli.py                 # 命令行界面
└── __init__.py
```

## ⚙️ 配置

框架使用YAML配置文件 (`config.yaml`)：

```yaml
analyzer:
  max_workers: 4           # 并发线程数
  timeout: 30              # 超时时间
  min_confidence: 0.1      # 最小置信度
  max_attempts: 50         # 最大尝试次数

logging:
  level: INFO              # 日志级别
  file: "logs/crypto_analyzer.log"
```

## 🧪 测试

运行测试脚本验证框架功能：

```bash
python test_framework.py
```

## 📊 置信度评分

框架使用多维度评分系统：

- **算法匹配度** (25%): 数据特征与算法的匹配程度
- **可读性** (35%): 解密结果的可读性
- **字符分布** (20%): 字符频率分布的合理性
- **语义分析** (20%): 文本的语义合理性

## 🔧 扩展开发

### 添加新的检测器

1. 继承 `DetectorBase` 类
2. 实现 `can_detect()` 和 `decrypt()` 方法
3. 将检测器放在相应的类型目录下

```python
from crypto_analyzer.core.detector_base import DetectorBase

class MyDetector(DetectorBase):
    def __init__(self):
        super().__init__("MyAlgorithm", "encoding")
    
    def can_detect(self, data: str) -> float:
        # 实现检测逻辑
        return confidence_score
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        # 实现解密逻辑
        return attempts
```

## 📝 使用示例

### 示例1: Base64解码

```bash
$ python main.py analyze "SGVsbG8gV29ybGQ="

输入数据: SGVsbG8gV29ybGQ=
分析时间: 0.045秒
检测到的算法: Base64, Base32, Base16

最佳结果:
  算法: Base64
  置信度: 89.50%
  结果: Hello World
```

### 示例2: 哈希识别

```bash
$ python main.py analyze "5d41402abc4b2a76b9719d911017c592"

输入数据: 5d41402abc4b2a76b9719d911017c592
分析时间: 0.023秒
检测到的算法: MD5, Base16

最佳结果:
  算法: MD5
  置信度: 95.00%
  结果: MD5 Hash: 5D41402ABC4B2A76B9719D911017C592
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 🙏 致谢

感谢所有为密码学和信息安全领域做出贡献的研究者和开发者。

---

**⚠️ 免责声明**: 此工具仅用于教育和合法的安全研究目的。请勿用于非法活动。
