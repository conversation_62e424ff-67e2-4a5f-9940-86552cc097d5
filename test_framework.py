#!/usr/bin/env python3
"""
框架测试脚本
============

测试密码分析框架的基本功能。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crypto_analyzer import CryptoAnalyzer


def test_basic_functionality():
    """测试基本功能"""
    print("🔍 测试密码分析框架...")
    
    # 创建分析器
    analyzer = CryptoAnalyzer()
    
    # 测试数据
    test_cases = [
        ("SGVsbG8gV29ybGQ=", "Base64编码"),
        ("48656c6c6f20576f726c64", "十六进制编码"),
        ("Uryyb Jbeyq", "ROT13编码"),
        ("5d41402abc4b2a76b9719d911017c592", "MD5哈希"),
        ("aHR0cHM6Ly9leGFtcGxlLmNvbQ==", "Base64编码的URL"),
    ]
    
    print(f"\n📊 测试 {len(test_cases)} 个样本...")
    
    for i, (data, expected) in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {expected} ---")
        print(f"输入: {data}")
        
        try:
            # 分析数据
            result = analyzer.analyze(data)
            
            print(f"分析时间: {result.analysis_time:.3f}秒")
            print(f"检测到的算法: {result.detected_algorithms}")
            
            if result.best_result:
                print(f"最佳结果:")
                print(f"  算法: {result.best_result.algorithm}")
                print(f"  置信度: {result.best_result.confidence:.2%}")
                print(f"  解密结果: {result.best_result.result}")
            else:
                print("❌ 未找到有效的解密结果")
                
            # 显示成功的尝试数量
            successful = result.get_successful_attempts()
            print(f"成功尝试: {len(successful)}/{len(result.attempts)}")
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    print(f"\n✅ 测试完成!")


def test_supported_algorithms():
    """测试支持的算法列表"""
    print("\n🔧 检查支持的算法...")
    
    analyzer = CryptoAnalyzer()
    algorithms = analyzer.get_supported_algorithms()
    
    total_count = 0
    for category, algs in algorithms.items():
        if algs:
            print(f"\n{category.upper()} ({len(algs)} 个):")
            for alg in algs:
                print(f"  ✓ {alg}")
            total_count += len(algs)
    
    print(f"\n📈 总计支持 {total_count} 种算法")


def test_analyzer_status():
    """测试分析器状态"""
    print("\n📋 检查分析器状态...")
    
    analyzer = CryptoAnalyzer()
    status = analyzer.get_status()
    
    pm_status = status['plugin_manager_status']
    print(f"总检测器数: {pm_status['total_detectors']}")
    print(f"启用检测器数: {pm_status['enabled_detectors']}")
    
    print("\n按类型分组:")
    for category, count in pm_status['categories'].items():
        print(f"  {category}: {count}")


if __name__ == '__main__':
    print("🚀 密码分析框架测试")
    print("=" * 50)
    
    try:
        test_analyzer_status()
        test_supported_algorithms()
        test_basic_functionality()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
