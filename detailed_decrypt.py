#!/usr/bin/env python3
"""
详细解密分析
============

获取完整的解密内容。
"""

import base64
import binascii
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

def analyze_and_decrypt():
    """分析并解密两个加密串"""
    
    # 第一个加密串
    first_string = "yX67CAY2RrMSJ1TxneWBANxXBK5wL6Rvk2bDRa+DKUspUee9v69x1s3TH1rv8tP4wG6sJnnrJmZfMM7TEqmnisgTL6BdEeeLi7oyL6S3MwvEjGQlqs0iPesluloSgPHLd9fQCwbcvjzG10olmLTRox7llp7QHyQ1I4Jj76o4+Ldetv9hUFILGWA9t2d6cLdAlVuymsfY95N9rBQYn7gyHMzGp8h6Xx7dIPQzOUoq6F++fSQfNbp7EcW8WC6BGaKpUxBRQS1J5AG2G5BqhbnedXY4w7GogDKI1hALiTgcsoF8NHy7/3gwZ8o4AX6CvvXPmaefqio3cQZMiNcK9P0sU8l5NAwEuUhsnO4df+JrAV9vY0O3uw6zgrRDaDZvjm0elQKTpXQtWJzN8DolK51ddhELbxpZx7u6rF3SoX1DlPJOpXvoqe2zmV4YzD6O3QG9UQU4Nc4FmX8hNAEm8XRuavVaK6wWX17I4jRA5eG9zrNvl0SybAS6PbPGqCfp04uIcKhpFDPrALGzdy/yWi/2GKXSmGbrxbIFJ2v4DYPX0Gw="
    
    # 第二个加密串
    second_string = "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"
    
    print("🔍 详细解密分析")
    print("=" * 60)
    
    # 分析第一个串
    print("\n📊 第一个加密串分析:")
    print(f"长度: {len(first_string)} 字符")
    
    try:
        first_decoded = base64.b64decode(first_string)
        print(f"Base64解码后长度: {len(first_decoded)} 字节")
        print(f"是否16字节对齐: {len(first_decoded) % 16 == 0}")
        
        # 尝试常见密钥解密第一个串
        print("\n尝试解密第一个串...")
        decrypt_with_common_keys(first_decoded, "第一个串")
        
    except Exception as e:
        print(f"第一个串Base64解码失败: {e}")
    
    # 分析第二个串
    print("\n📊 第二个加密串分析:")
    print(f"长度: {len(second_string)} 字符")
    
    try:
        second_decoded = base64.b64decode(second_string)
        print(f"Base64解码后长度: {len(second_decoded)} 字节")
        print(f"是否16字节对齐: {len(second_decoded) % 16 == 0}")
        
        # 尝试常见密钥解密第二个串
        print("\n尝试解密第二个串...")
        result = decrypt_with_common_keys(second_decoded, "第二个串")
        
    except Exception as e:
        print(f"第二个串Base64解码失败: {e}")

def decrypt_with_common_keys(ciphertext, label):
    """使用常见密钥尝试解密"""
    
    # 常见的AES密钥
    common_keys = [
        b"1234567890123456",  # 16字节
        b"password12345678",  # 16字节  
        b"secretkey1234567",  # 16字节
        b"0123456789abcdef",  # 16字节
        b"abcdef0123456789",  # 16字节
        b"key1234567890123",  # 16字节
        b"mypassword123456",  # 16字节
    ]
    
    success_count = 0
    
    for i, key in enumerate(common_keys):
        print(f"\n尝试密钥 {i+1}: {key.decode('utf-8', errors='ignore')}")
        
        # 尝试ECB模式
        result = try_aes_mode(ciphertext, key, "ECB")
        if result:
            print(f"✅ AES-ECB解密成功!")
            print(f"完整内容:\n{result}")
            success_count += 1
            
        # 尝试CBC模式 (零IV)
        result = try_aes_mode(ciphertext, key, "CBC")
        if result:
            print(f"✅ AES-CBC解密成功!")
            print(f"完整内容:\n{result}")
            success_count += 1
    
    if success_count == 0:
        print(f"❌ {label}未能用常见密钥解密")
    else:
        print(f"✅ {label}共找到 {success_count} 种解密方法")

def try_aes_mode(ciphertext, key, mode_name):
    """尝试特定AES模式解密"""
    try:
        if mode_name == "ECB":
            cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend())
        elif mode_name == "CBC":
            cipher = Cipher(algorithms.AES(key), modes.CBC(b'\x00' * 16), backend=default_backend())
        else:
            return None
            
        decryptor = cipher.decryptor()
        decrypted = decryptor.update(ciphertext) + decryptor.finalize()
        
        # 尝试解码为文本
        try:
            text = decrypted.decode('utf-8', errors='ignore')
            # 检查是否包含可打印字符
            printable_ratio = sum(1 for c in text if c.isprintable()) / len(text)
            if printable_ratio > 0.7:  # 至少70%可打印字符
                return text
        except:
            pass
            
        # 尝试其他编码
        for encoding in ['gbk', 'latin-1']:
            try:
                text = decrypted.decode(encoding, errors='ignore')
                printable_ratio = sum(1 for c in text if c.isprintable()) / len(text)
                if printable_ratio > 0.7:
                    return f"[{encoding}编码] {text}"
            except:
                continue
                
    except Exception as e:
        pass
    
    return None

if __name__ == '__main__':
    analyze_and_decrypt()
