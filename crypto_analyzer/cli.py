"""
命令行界面
==========

提供密码分析工具的命令行接口。
"""

import click
import yaml
import json
import sys
from pathlib import Path
from typing import Optional, List
from loguru import logger

from .core.analyzer import CryptoAnalyzer
from .core.result import AnalysisResult


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), 
              help='配置文件路径')
@click.option('--verbose', '-v', is_flag=True, 
              help='启用详细输出')
@click.option('--quiet', '-q', is_flag=True, 
              help='静默模式')
@click.pass_context
def cli(ctx, config: Optional[str], verbose: bool, quiet: bool):
    """
    企业级密码分析工具
    
    支持多种加密算法的自动识别和解密。
    """
    # 确保上下文对象存在
    ctx.ensure_object(dict)
    
    # 加载配置
    if config:
        config_path = Path(config)
    else:
        config_path = Path('config.yaml')
    
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            ctx.obj['config'] = yaml.safe_load(f)
    else:
        ctx.obj['config'] = {}
    
    # 设置日志级别
    if quiet:
        logger.remove()
    elif verbose:
        logger.add(sys.stderr, level="DEBUG")
    else:
        logger.add(sys.stderr, level="INFO")
    
    ctx.obj['verbose'] = verbose
    ctx.obj['quiet'] = quiet


@cli.command()
@click.argument('data', type=str)
@click.option('--output', '-o', type=click.Path(), 
              help='输出文件路径')
@click.option('--format', '-f', type=click.Choice(['json', 'yaml', 'text']), 
              default='text', help='输出格式')
@click.option('--max-results', '-n', type=int, default=10, 
              help='最大显示结果数')
@click.option('--show-failures', is_flag=True, 
              help='显示失败的尝试')
@click.option('--key', type=str, 
              help='用于对称加密的密钥')
@click.option('--wordlist', type=click.Path(exists=True), 
              help='用于哈希破解的字典文件')
@click.pass_context
def analyze(ctx, data: str, output: Optional[str], format: str, 
           max_results: int, show_failures: bool, key: Optional[str], 
           wordlist: Optional[str]):
    """
    分析单个加密字符串
    
    DATA: 要分析的加密数据
    """
    config = ctx.obj.get('config', {})
    verbose = ctx.obj.get('verbose', False)
    
    # 创建分析器
    analyzer = CryptoAnalyzer(config.get('analyzer', {}))
    
    # 准备额外参数
    kwargs = {}
    if key:
        kwargs['key'] = key
    if wordlist:
        with open(wordlist, 'r', encoding='utf-8') as f:
            kwargs['wordlist'] = [line.strip() for line in f if line.strip()]
    
    try:
        # 执行分析
        if not ctx.obj.get('quiet', False):
            click.echo(f"正在分析: {data[:50]}{'...' if len(data) > 50 else ''}")
        
        result = analyzer.analyze(data, **kwargs)
        
        # 格式化输出
        output_text = format_result(result, format, max_results, show_failures, verbose)
        
        # 输出结果
        if output:
            with open(output, 'w', encoding='utf-8') as f:
                f.write(output_text)
            if not ctx.obj.get('quiet', False):
                click.echo(f"结果已保存到: {output}")
        else:
            click.echo(output_text)
            
    except Exception as e:
        logger.error(f"分析过程中出错: {e}")
        sys.exit(1)


@cli.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(), 
              help='输出文件路径')
@click.option('--format', '-f', type=click.Choice(['json', 'yaml', 'text']), 
              default='json', help='输出格式')
@click.option('--max-results', '-n', type=int, default=5, 
              help='每个输入的最大显示结果数')
@click.option('--key', type=str, 
              help='用于对称加密的密钥')
@click.option('--wordlist', type=click.Path(exists=True), 
              help='用于哈希破解的字典文件')
@click.pass_context
def batch(ctx, input_file: str, output: Optional[str], format: str, 
         max_results: int, key: Optional[str], wordlist: Optional[str]):
    """
    批量分析文件中的加密字符串
    
    INPUT_FILE: 包含待分析数据的文件，每行一个
    """
    config = ctx.obj.get('config', {})
    quiet = ctx.obj.get('quiet', False)
    
    # 创建分析器
    analyzer = CryptoAnalyzer(config.get('analyzer', {}))
    
    # 准备额外参数
    kwargs = {}
    if key:
        kwargs['key'] = key
    if wordlist:
        with open(wordlist, 'r', encoding='utf-8') as f:
            kwargs['wordlist'] = [line.strip() for line in f if line.strip()]
    
    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f if line.strip()]
        
        if not quiet:
            click.echo(f"正在批量分析 {len(lines)} 个项目...")
        
        results = []
        
        # 分析每一行
        with click.progressbar(lines, label='分析进度') as bar:
            for line in bar:
                try:
                    result = analyzer.analyze(line, **kwargs)
                    results.append(result)
                except Exception as e:
                    logger.warning(f"分析 '{line[:30]}...' 时出错: {e}")
                    # 创建错误结果
                    error_result = AnalysisResult(input_data=line)
                    error_result.metadata['error'] = str(e)
                    results.append(error_result)
        
        # 格式化输出
        if format == 'json':
            output_text = json.dumps([r.to_dict() for r in results], 
                                   indent=2, ensure_ascii=False)
        elif format == 'yaml':
            output_text = yaml.dump([r.to_dict() for r in results], 
                                  default_flow_style=False, allow_unicode=True)
        else:  # text
            output_parts = []
            for i, result in enumerate(results, 1):
                output_parts.append(f"\n=== 结果 {i} ===")
                output_parts.append(format_result(result, 'text', max_results, False, False))
            output_text = '\n'.join(output_parts)
        
        # 输出结果
        if output:
            with open(output, 'w', encoding='utf-8') as f:
                f.write(output_text)
            if not quiet:
                click.echo(f"批量分析结果已保存到: {output}")
        else:
            click.echo(output_text)
            
    except Exception as e:
        logger.error(f"批量分析过程中出错: {e}")
        sys.exit(1)


@cli.command()
@click.pass_context
def list_algorithms(ctx):
    """列出支持的算法"""
    config = ctx.obj.get('config', {})
    analyzer = CryptoAnalyzer(config.get('analyzer', {}))
    
    algorithms = analyzer.get_supported_algorithms()
    
    click.echo("支持的算法:")
    for category, algs in algorithms.items():
        if algs:
            click.echo(f"\n{category.upper()}:")
            for alg in algs:
                click.echo(f"  - {alg}")


@cli.command()
@click.pass_context
def status(ctx):
    """显示分析器状态"""
    config = ctx.obj.get('config', {})
    analyzer = CryptoAnalyzer(config.get('analyzer', {}))
    
    status_info = analyzer.get_status()
    
    click.echo("分析器状态:")
    click.echo(f"总检测器数: {status_info['plugin_manager_status']['total_detectors']}")
    click.echo(f"启用检测器数: {status_info['plugin_manager_status']['enabled_detectors']}")
    
    click.echo("\n按类型分组:")
    for category, count in status_info['plugin_manager_status']['categories'].items():
        click.echo(f"  {category}: {count}")


def format_result(result: AnalysisResult, format: str, max_results: int, 
                 show_failures: bool, verbose: bool) -> str:
    """格式化分析结果"""
    if format == 'json':
        return result.to_json()
    elif format == 'yaml':
        return yaml.dump(result.to_dict(), default_flow_style=False, allow_unicode=True)
    else:  # text
        lines = []
        lines.append(f"输入数据: {result.input_data}")
        lines.append(f"分析时间: {result.analysis_time:.3f}秒")
        
        if result.detected_algorithms:
            lines.append(f"检测到的算法: {', '.join(result.detected_algorithms)}")
        
        if result.best_result:
            lines.append(f"\n最佳结果:")
            lines.append(f"  算法: {result.best_result.algorithm}")
            lines.append(f"  置信度: {result.best_result.confidence:.2%}")
            lines.append(f"  结果: {result.best_result.result}")
            if result.best_result.key:
                lines.append(f"  密钥: {result.best_result.key}")
        
        # 显示其他成功的尝试
        successful = result.get_successful_attempts()
        if len(successful) > 1:
            lines.append(f"\n其他成功的尝试:")
            for attempt in successful[1:max_results]:
                lines.append(f"  - {attempt.algorithm}: {attempt.confidence:.2%}")
                if verbose:
                    lines.append(f"    结果: {attempt.result[:100]}...")
        
        # 显示失败的尝试（如果请求）
        if show_failures:
            failed = [a for a in result.attempts if not a.success]
            if failed:
                lines.append(f"\n失败的尝试:")
                for attempt in failed[:max_results]:
                    lines.append(f"  - {attempt.algorithm}: {attempt.error_message}")
        
        return '\n'.join(lines)


if __name__ == '__main__':
    cli()
