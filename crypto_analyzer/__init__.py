"""
企业级密码分析框架
====================

一个功能强大的密码分析工具，支持多种加密算法的自动识别和解密。

主要功能：
- 自动识别加密/编码算法类型
- 智能解密和解码
- 置信度评分系统
- 批量处理支持
- 详细分析报告

支持的算法：
- 编码：Base64, Base32, Base16, URL编码, HTML实体编码
- 对称加密：AES, DES, 3DES, Blowfish
- 非对称加密：RSA
- 哈希算法：MD5, SHA1, SHA256, SHA512
- 古典密码：凯撒密码, 维吉尼亚密码, 栅栏密码
- 其他：ROT13, 摩尔斯电码, 二进制转换
"""

__version__ = "1.0.0"
__author__ = "Claude 4.0 Sonnet"
__email__ = "<EMAIL>"

from .core.analyzer import CryptoAnalyzer
from .core.detector_base import DetectorBase
from .core.plugin_manager import PluginManager
from .core.confidence_scorer import ConfidenceScorer
from .core.result import AnalysisResult

__all__ = [
    "CryptoAnalyzer",
    "DetectorBase", 
    "PluginManager",
    "ConfidenceScorer",
    "AnalysisResult",
]
