"""
SHA256哈希检测器
================

检测SHA256哈希值。
"""

import re
import hashlib
from typing import List
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class SHA256Detector(DetectorBase):
    """SHA256哈希检测器"""
    
    def __init__(self):
        super().__init__("SHA256", "hash")
        self.priority = 85
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是SHA256哈希
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        # 移除空白字符
        clean_data = re.sub(r'\s', '', data)
        
        confidence = 0.0
        
        # 1. 长度检查 - SHA256哈希固定64个字符
        if len(clean_data) == 64:
            confidence += 0.5
        else:
            return 0.0
        
        # 2. 字符集检查 - 只包含十六进制字符
        if re.match(r'^[0-9A-Fa-f]{64}$', clean_data):
            confidence += 0.4
        else:
            return 0.0
        
        # 3. 检查是否全为相同字符（不太可能是真实哈希）
        if len(set(clean_data.lower())) > 1:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        识别SHA256哈希（哈希不能被"解密"，只能识别）
        
        Args:
            data: 待识别的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 识别结果列表
        """
        attempts = []
        
        # 移除空白字符
        clean_data = re.sub(r'\s', '', data).lower()
        
        # SHA256识别
        attempt = self._identify_sha256(clean_data)
        attempts.append(attempt)
        
        # 如果提供了字典，尝试暴力破解
        wordlist = kwargs.get('wordlist', [])
        if wordlist:
            crack_attempt = self._try_crack_sha256(clean_data, wordlist)
            attempts.append(crack_attempt)
        
        return attempts
    
    def _identify_sha256(self, data: str) -> DecryptionAttempt:
        """识别SHA256哈希"""
        try:
            # 验证格式
            if len(data) == 64 and re.match(r'^[0-9a-f]{64}$', data):
                return self._create_attempt(
                    algorithm="SHA256",
                    data=data,
                    result=f"SHA256 Hash: {data.upper()}",
                    success=True,
                    confidence=0.9
                )
            else:
                return self._create_attempt(
                    algorithm="SHA256",
                    data=data,
                    success=False,
                    error="Invalid SHA256 format"
                )
                
        except Exception as e:
            return self._create_attempt(
                algorithm="SHA256",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _try_crack_sha256(self, hash_value: str, wordlist: List[str]) -> DecryptionAttempt:
        """尝试暴力破解SHA256哈希"""
        try:
            for word in wordlist[:1000]:  # 限制尝试次数
                # 计算单词的SHA256哈希
                word_hash = hashlib.sha256(word.encode('utf-8')).hexdigest()
                
                if word_hash.lower() == hash_value.lower():
                    return self._create_attempt(
                        algorithm="SHA256 (cracked)",
                        data=hash_value,
                        result=word,
                        success=True,
                        confidence=1.0
                    )
            
            return self._create_attempt(
                algorithm="SHA256 (cracked)",
                data=hash_value,
                success=False,
                error="Hash not found in wordlist"
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="SHA256 (cracked)",
                data=hash_value,
                success=False,
                error=str(e)
            )
