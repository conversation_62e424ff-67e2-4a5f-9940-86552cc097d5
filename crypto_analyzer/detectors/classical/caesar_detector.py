"""
凯撒密码检测器
==============

检测和解密凯撒密码。
"""

import re
from typing import List
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class CaesarDetector(DetectorBase):
    """凯撒密码检测器"""
    
    def __init__(self):
        super().__init__("Caesar", "classical")
        self.priority = 70
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是凯撒密码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        # 凯撒密码主要用于字母文本
        letters = re.findall(r'[a-zA-Z]', data)
        if not letters:
            return 0.0
        
        letter_ratio = len(letters) / len(data)
        
        # 如果字母比例较高，可能是凯撒密码
        if letter_ratio > 0.7:
            return min(letter_ratio, 0.8)
        elif letter_ratio > 0.5:
            return min(letter_ratio * 0.8, 0.6)
        else:
            return 0.0
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解密凯撒密码
        
        Args:
            data: 待解密的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解密尝试结果列表
        """
        attempts = []
        
        # 尝试所有可能的偏移量（1-25）
        for shift in range(1, 26):
            attempt = self._try_decrypt_caesar(data, shift)
            attempts.append(attempt)
        
        return attempts
    
    def _try_decrypt_caesar(self, data: str, shift: int) -> DecryptionAttempt:
        """尝试指定偏移量的凯撒解密"""
        try:
            decrypted_chars = []
            
            for char in data:
                if char.isalpha():
                    # 确定是大写还是小写
                    if char.isupper():
                        # 大写字母
                        shifted = chr((ord(char) - ord('A') - shift) % 26 + ord('A'))
                    else:
                        # 小写字母
                        shifted = chr((ord(char) - ord('a') - shift) % 26 + ord('a'))
                    decrypted_chars.append(shifted)
                else:
                    # 非字母字符保持不变
                    decrypted_chars.append(char)
            
            decrypted_text = ''.join(decrypted_chars)
            
            # 评估解密结果的质量
            success = self._is_printable_text(decrypted_text)
            
            # 计算置信度
            confidence = self._calculate_caesar_confidence(decrypted_text, shift)
            
            return self._create_attempt(
                algorithm=f"Caesar (shift {shift})",
                data=data,
                result=decrypted_text,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm=f"Caesar (shift {shift})",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _calculate_caesar_confidence(self, text: str, shift: int) -> float:
        """计算凯撒解密的置信度"""
        if not text:
            return 0.0
        
        confidence = 0.0
        
        # 1. 基础可读性检查
        if self._is_printable_text(text):
            confidence += 0.4
        
        # 2. 英文单词检查
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        if words:
            # 简单的英文单词检查
            common_words = {
                'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
                'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
                'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy',
                'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'
            }
            
            word_count = len(words)
            common_count = sum(1 for word in words if word in common_words)
            
            if word_count > 0:
                common_ratio = common_count / word_count
                confidence += common_ratio * 0.4
        
        # 3. 字母频率分析
        letters = ''.join(c.lower() for c in text if c.isalpha())
        if letters:
            # 检查字母'e'的频率（英文中最常见）
            e_frequency = letters.count('e') / len(letters)
            # 英文中'e'的频率约为12.7%
            if 0.08 <= e_frequency <= 0.18:
                confidence += 0.2
        
        return min(confidence, 1.0)
