"""
维吉尼亚密码检测器
==================

检测和解密维吉尼亚密码（Vigenère Cipher）。
"""

import re
from typing import List, Tuple
from collections import Counter
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class VigenereDetector(DetectorBase):
    """维吉尼亚密码检测器"""
    
    def __init__(self):
        super().__init__("Vigenere", "classical")
        self.priority = 65
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是维吉尼亚密码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        # 维吉尼亚密码主要用于字母文本
        letters = re.findall(r'[a-zA-Z]', data)
        if not letters:
            return 0.0
        
        letter_ratio = len(letters) / len(data)
        
        # 检查重复模式（Kasiski测试的简化版本）
        confidence = 0.0
        
        if letter_ratio > 0.7:
            confidence += min(letter_ratio * 0.5, 0.4)
            
            # 检查是否有重复的3字符模式
            text = ''.join(letters).upper()
            if len(text) >= 6:
                patterns = {}
                for i in range(len(text) - 2):
                    pattern = text[i:i+3]
                    if pattern in patterns:
                        patterns[pattern].append(i)
                    else:
                        patterns[pattern] = [i]
                
                # 如果有重复模式，可能是维吉尼亚密码
                repeated_patterns = {k: v for k, v in patterns.items() if len(v) > 1}
                if repeated_patterns:
                    confidence += min(len(repeated_patterns) * 0.1, 0.3)
        
        return min(confidence, 0.8)  # 维吉尼亚密码检测置信度不会太高
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解密维吉尼亚密码
        
        Args:
            data: 待解密的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解密尝试结果列表
        """
        attempts = []
        
        # 提取字母
        clean_data = ''.join(c.upper() for c in data if c.isalpha())
        
        if len(clean_data) < 6:
            return [self._create_attempt(
                algorithm="Vigenere",
                data=data,
                success=False,
                error="Text too short for Vigenere analysis"
            )]
        
        # 尝试确定密钥长度
        possible_key_lengths = self._find_key_lengths(clean_data)
        
        # 对每个可能的密钥长度尝试解密
        for key_length in possible_key_lengths[:5]:  # 限制尝试次数
            attempt = self._try_decrypt_with_key_length(clean_data, key_length)
            if attempt.success:
                attempts.append(attempt)
        
        # 如果没有找到好的密钥长度，尝试常见长度
        if not attempts:
            for key_length in [3, 4, 5, 6, 7, 8]:
                if key_length not in possible_key_lengths:
                    attempt = self._try_decrypt_with_key_length(clean_data, key_length)
                    attempts.append(attempt)
        
        return attempts
    
    def _find_key_lengths(self, text: str) -> List[int]:
        """使用Kasiski测试寻找可能的密钥长度"""
        # 寻找重复的3字符模式
        patterns = {}
        for i in range(len(text) - 2):
            pattern = text[i:i+3]
            if pattern in patterns:
                patterns[pattern].append(i)
            else:
                patterns[pattern] = [i]
        
        # 计算重复模式之间的距离
        distances = []
        for pattern, positions in patterns.items():
            if len(positions) > 1:
                for i in range(len(positions) - 1):
                    distance = positions[i + 1] - positions[i]
                    distances.append(distance)
        
        if not distances:
            return [3, 4, 5, 6]  # 默认尝试这些长度
        
        # 找到距离的公约数
        possible_lengths = []
        for length in range(2, 21):  # 密钥长度通常不超过20
            if sum(1 for d in distances if d % length == 0) >= len(distances) * 0.3:
                possible_lengths.append(length)
        
        return sorted(possible_lengths) if possible_lengths else [3, 4, 5, 6]
    
    def _try_decrypt_with_key_length(self, text: str, key_length: int) -> DecryptionAttempt:
        """尝试用指定密钥长度解密"""
        try:
            # 将文本分成key_length个子序列
            subsequences = ['' for _ in range(key_length)]
            for i, char in enumerate(text):
                subsequences[i % key_length] += char
            
            # 对每个子序列进行频率分析，找到最可能的偏移量
            key_chars = []
            for subseq in subsequences:
                if subseq:
                    best_shift = self._find_best_shift(subseq)
                    key_chars.append(chr(best_shift + ord('A')))
                else:
                    key_chars.append('A')
            
            key = ''.join(key_chars)
            
            # 使用找到的密钥解密
            decrypted_text = self._decrypt_with_key(text, key)
            
            # 评估解密结果
            success = self._is_printable_text(decrypted_text)
            confidence = self._calculate_vigenere_confidence(decrypted_text, key)
            
            return self._create_attempt(
                algorithm=f"Vigenere (key: {key})",
                data=text,
                result=decrypted_text,
                success=success,
                confidence=confidence,
                key=key
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm=f"Vigenere (length: {key_length})",
                data=text,
                success=False,
                error=str(e)
            )
    
    def _find_best_shift(self, text: str) -> int:
        """通过频率分析找到最佳偏移量"""
        # 英文字母频率（简化）
        english_freq = [8.12, 1.49, 2.78, 4.25, 12.02, 2.23, 2.02, 6.09, 6.97, 0.15,
                       0.77, 4.03, 2.41, 6.75, 7.51, 1.93, 0.10, 5.99, 6.33, 9.06,
                       2.76, 0.98, 2.36, 0.15, 1.97, 0.07]
        
        best_score = float('inf')
        best_shift = 0
        
        for shift in range(26):
            # 解密并计算频率
            decrypted = ''.join(chr((ord(c) - ord('A') - shift) % 26 + ord('A')) for c in text)
            freq = [0] * 26
            
            for c in decrypted:
                if 'A' <= c <= 'Z':
                    freq[ord(c) - ord('A')] += 1
            
            # 计算卡方统计量
            total = sum(freq)
            if total == 0:
                continue
                
            chi_squared = 0
            for i in range(26):
                expected = english_freq[i] * total / 100
                if expected > 0:
                    chi_squared += (freq[i] - expected) ** 2 / expected
            
            if chi_squared < best_score:
                best_score = chi_squared
                best_shift = shift
        
        return best_shift
    
    def _decrypt_with_key(self, text: str, key: str) -> str:
        """使用密钥解密维吉尼亚密码"""
        decrypted = []
        key_length = len(key)
        
        for i, char in enumerate(text):
            if char.isalpha():
                key_char = key[i % key_length]
                shift = ord(key_char.upper()) - ord('A')
                
                if char.isupper():
                    decrypted_char = chr((ord(char) - ord('A') - shift) % 26 + ord('A'))
                else:
                    decrypted_char = chr((ord(char.upper()) - ord('A') - shift) % 26 + ord('A'))
                    decrypted_char = decrypted_char.lower()
                
                decrypted.append(decrypted_char)
            else:
                decrypted.append(char)
        
        return ''.join(decrypted)
    
    def _calculate_vigenere_confidence(self, text: str, key: str) -> float:
        """计算维吉尼亚密码解密的置信度"""
        if not text:
            return 0.0
        
        confidence = 0.0
        
        # 1. 基础可读性检查
        if self._is_printable_text(text):
            confidence += 0.3
        
        # 2. 英文单词检查
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        if words:
            common_words = {
                'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
                'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his'
            }
            
            word_count = len(words)
            common_count = sum(1 for word in words if word in common_words)
            
            if word_count > 0:
                common_ratio = common_count / word_count
                confidence += common_ratio * 0.4
        
        # 3. 密钥合理性
        if 3 <= len(key) <= 12:  # 合理的密钥长度
            confidence += 0.2
        
        # 4. 字母频率分析
        letters = ''.join(c.lower() for c in text if c.isalpha())
        if letters:
            e_frequency = letters.count('e') / len(letters)
            if 0.08 <= e_frequency <= 0.18:  # 英文中'e'的正常频率范围
                confidence += 0.1
        
        return min(confidence, 1.0)
