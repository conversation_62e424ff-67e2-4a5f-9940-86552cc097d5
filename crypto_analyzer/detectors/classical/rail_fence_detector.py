"""
栅栏密码检测器
==============

检测和解密栅栏密码（Rail Fence Cipher）。
"""

import re
from typing import List
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class RailFenceDetector(DetectorBase):
    """栅栏密码检测器"""
    
    def __init__(self):
        super().__init__("Rail Fence", "classical")
        self.priority = 60
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是栅栏密码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        # 栅栏密码主要用于字母文本
        letters = re.findall(r'[a-zA-Z]', data)
        if not letters:
            return 0.0
        
        letter_ratio = len(letters) / len(data)
        
        # 栅栏密码通常是纯文本，字母比例应该很高
        if letter_ratio > 0.8:
            return min(letter_ratio * 0.7, 0.7)  # 相对较低的初始置信度
        elif letter_ratio > 0.6:
            return min(letter_ratio * 0.5, 0.5)
        else:
            return 0.0
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解密栅栏密码
        
        Args:
            data: 待解密的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解密尝试结果列表
        """
        attempts = []
        
        # 移除非字母字符进行分析
        clean_data = ''.join(c for c in data if c.isalpha())
        
        # 尝试不同的栅栏数量（2到10）
        max_rails = min(10, len(clean_data) // 2)
        
        for rails in range(2, max_rails + 1):
            attempt = self._try_decrypt_rail_fence(clean_data, rails)
            attempts.append(attempt)
        
        return attempts
    
    def _try_decrypt_rail_fence(self, data: str, rails: int) -> DecryptionAttempt:
        """尝试指定栅栏数的解密"""
        try:
            if rails < 2 or len(data) < rails:
                raise ValueError(f"Invalid rails count: {rails}")
            
            # 创建栅栏模式
            fence = [[] for _ in range(rails)]
            rail = 0
            direction = 1
            
            # 确定每个位置应该在哪个栅栏
            positions = []
            for i in range(len(data)):
                positions.append(rail)
                rail += direction
                if rail == rails - 1 or rail == 0:
                    direction = -direction
            
            # 将字符分配到对应的栅栏
            char_index = 0
            for rail_num in range(rails):
                for pos in range(len(positions)):
                    if positions[pos] == rail_num:
                        if char_index < len(data):
                            fence[rail_num].append(data[char_index])
                            char_index += 1
            
            # 重建原始文本
            decrypted_chars = [''] * len(data)
            char_index = 0
            
            for rail_num in range(rails):
                rail_chars = fence[rail_num]
                rail_char_index = 0
                
                for pos in range(len(positions)):
                    if positions[pos] == rail_num and rail_char_index < len(rail_chars):
                        decrypted_chars[pos] = rail_chars[rail_char_index]
                        rail_char_index += 1
            
            decrypted_text = ''.join(decrypted_chars)
            
            # 评估解密结果
            success = self._is_printable_text(decrypted_text)
            confidence = self._calculate_rail_fence_confidence(decrypted_text, rails)
            
            return self._create_attempt(
                algorithm=f"Rail Fence ({rails} rails)",
                data=data,
                result=decrypted_text,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm=f"Rail Fence ({rails} rails)",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _calculate_rail_fence_confidence(self, text: str, rails: int) -> float:
        """计算栅栏密码解密的置信度"""
        if not text:
            return 0.0
        
        confidence = 0.0
        
        # 1. 基础可读性检查
        if self._is_printable_text(text):
            confidence += 0.4
        
        # 2. 英文单词检查
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        if words:
            # 简单的英文单词检查
            common_words = {
                'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
                'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
                'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy',
                'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'
            }
            
            word_count = len(words)
            common_count = sum(1 for word in words if word in common_words)
            
            if word_count > 0:
                common_ratio = common_count / word_count
                confidence += common_ratio * 0.4
        
        # 3. 栅栏数合理性（较少的栅栏数通常更可能）
        if rails <= 4:
            confidence += 0.1
        elif rails <= 6:
            confidence += 0.05
        
        # 4. 字母频率检查
        letters = ''.join(c.lower() for c in text if c.isalpha())
        if letters:
            # 检查常见字母的频率
            common_letters = 'etaoinshrdlu'
            common_count = sum(letters.count(c) for c in common_letters[:5])
            common_ratio = common_count / len(letters)
            
            if common_ratio > 0.4:
                confidence += 0.1
        
        return min(confidence, 1.0)
