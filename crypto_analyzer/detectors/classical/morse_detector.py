"""
摩尔斯电码检测器
================

检测和解码摩尔斯电码。
"""

import re
from typing import List, Dict
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class MorseDetector(DetectorBase):
    """摩尔斯电码检测器"""
    
    def __init__(self):
        super().__init__("Morse Code", "classical")
        self.priority = 55
        
        # 摩尔斯电码映射表
        self.morse_to_char = {
            '.-': 'A', '-...': 'B', '-.-.': 'C', '-..': 'D', '.': 'E',
            '..-.': 'F', '--.': 'G', '....': 'H', '..': 'I', '.---': 'J',
            '-.-': 'K', '.-..': 'L', '--': 'M', '-.': 'N', '---': 'O',
            '.--.': 'P', '--.-': 'Q', '.-.': 'R', '...': 'S', '-': 'T',
            '..-': 'U', '...-': 'V', '.--': 'W', '-..-': 'X', '-.--': 'Y',
            '--..': 'Z',
            '-----': '0', '.----': '1', '..---': '2', '...--': '3',
            '....-': '4', '.....': '5', '-....': '6', '--...': '7',
            '---..': '8', '----.': '9',
            '--..--': ',', '.-.-.-': '.', '..--..': '?', '.----.': "'",
            '-.-.--': '!', '-..-.': '/', '-.--.': '(', '-.--.-': ')',
            '.-...': '&', '---...': ':', '-.-.-.': ';', '-...-': '=',
            '.-.-.': '+', '-....-': '-', '..--.-': '_', '.-..-.': '"',
            '...-..-': '$', '.--.-.': '@'
        }
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是摩尔斯电码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        confidence = 0.0
        
        # 1. 检查是否主要包含点、横线和分隔符
        morse_chars = re.findall(r'[.\-]', data)
        if not morse_chars:
            return 0.0
        
        morse_ratio = len(morse_chars) / len(data)
        confidence += min(morse_ratio, 0.6)
        
        # 2. 检查是否有合理的分隔符
        # 常见分隔符：空格、斜杠、竖线等
        if re.search(r'[\s/|]', data):
            confidence += 0.2
        
        # 3. 检查模式 - 摩尔斯电码通常有规律的点横线组合
        morse_patterns = re.findall(r'[.\-]+', data)
        if morse_patterns:
            # 检查是否有有效的摩尔斯码
            valid_patterns = sum(1 for pattern in morse_patterns 
                               if pattern in self.morse_to_char)
            if valid_patterns > 0:
                confidence += min(valid_patterns / len(morse_patterns), 0.2)
        
        return min(confidence, 1.0)
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解码摩尔斯电码
        
        Args:
            data: 待解码的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解码尝试结果列表
        """
        attempts = []
        
        # 尝试不同的分隔符
        separators = [' ', '/', '|', '\t', '  ', '   ']
        
        for separator in separators:
            attempt = self._try_decode_with_separator(data, separator)
            if attempt.success:
                attempts.append(attempt)
        
        # 如果没有明显分隔符，尝试自动分割
        auto_attempt = self._try_decode_auto_split(data)
        attempts.append(auto_attempt)
        
        return attempts
    
    def _try_decode_with_separator(self, data: str, separator: str) -> DecryptionAttempt:
        """使用指定分隔符解码摩尔斯电码"""
        try:
            # 分割摩尔斯码
            morse_codes = data.split(separator)
            decoded_chars = []
            
            for morse_code in morse_codes:
                morse_code = morse_code.strip()
                if not morse_code:
                    continue
                
                if morse_code in self.morse_to_char:
                    decoded_chars.append(self.morse_to_char[morse_code])
                else:
                    # 如果有无法识别的码，标记为?
                    decoded_chars.append('?')
            
            decoded_text = ''.join(decoded_chars)
            
            # 计算成功率
            total_codes = len([code for code in morse_codes if code.strip()])
            unknown_codes = decoded_text.count('?')
            success_rate = (total_codes - unknown_codes) / total_codes if total_codes > 0 else 0
            
            success = success_rate > 0.7 and self._is_printable_text(decoded_text.replace('?', ''))
            confidence = success_rate * 0.8 if success else success_rate * 0.3
            
            return self._create_attempt(
                algorithm=f"Morse Code (sep: '{separator}')",
                data=data,
                result=decoded_text,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm=f"Morse Code (sep: '{separator}')",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _try_decode_auto_split(self, data: str) -> DecryptionAttempt:
        """自动分割并解码摩尔斯电码"""
        try:
            # 尝试识别可能的摩尔斯码模式
            # 使用正则表达式找到所有可能的摩尔斯码
            possible_codes = re.findall(r'[.\-]+', data)
            
            decoded_chars = []
            for code in possible_codes:
                if code in self.morse_to_char:
                    decoded_chars.append(self.morse_to_char[code])
                else:
                    decoded_chars.append('?')
            
            decoded_text = ''.join(decoded_chars)
            
            # 计算成功率
            total_codes = len(possible_codes)
            unknown_codes = decoded_text.count('?')
            success_rate = (total_codes - unknown_codes) / total_codes if total_codes > 0 else 0
            
            success = success_rate > 0.6 and self._is_printable_text(decoded_text.replace('?', ''))
            confidence = success_rate * 0.7 if success else success_rate * 0.2
            
            return self._create_attempt(
                algorithm="Morse Code (auto-split)",
                data=data,
                result=decoded_text,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="Morse Code (auto-split)",
                data=data,
                success=False,
                error=str(e)
            )
