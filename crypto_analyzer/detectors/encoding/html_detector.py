"""
HTML实体编码检测器
==================

检测和解码HTML实体编码的数据。
"""

import re
import html
from typing import List, Dict
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class HTMLDetector(DetectorBase):
    """HTML实体编码检测器"""
    
    def __init__(self):
        super().__init__("HTML Entities", "encoding")
        self.priority = 70
        
        # 常见HTML实体映射
        self.common_entities = {
            '&lt;': '<',
            '&gt;': '>',
            '&amp;': '&',
            '&quot;': '"',
            '&apos;': "'",
            '&nbsp;': ' ',
            '&copy;': '©',
            '&reg;': '®',
            '&trade;': '™',
        }
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是HTML实体编码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        confidence = 0.0
        
        # 1. 检查命名实体
        named_entities = re.findall(r'&[a-zA-Z][a-zA-Z0-9]*;', data)
        if named_entities:
            entity_ratio = len(named_entities) * 4 / len(data)  # 平均实体长度约4
            confidence += min(entity_ratio * 2, 0.4)
        
        # 2. 检查数字实体 (&#数字; 或 &#x十六进制;)
        numeric_entities = re.findall(r'&#x?[0-9A-Fa-f]+;', data)
        if numeric_entities:
            numeric_ratio = len(numeric_entities) * 6 / len(data)  # 平均长度约6
            confidence += min(numeric_ratio * 2, 0.4)
        
        # 3. 检查常见实体
        common_count = sum(1 for entity in self.common_entities.keys() if entity in data)
        if common_count > 0:
            confidence += min(common_count * 0.1, 0.2)
        
        return min(confidence, 1.0)
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解码HTML实体编码数据
        
        Args:
            data: 待解码的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解码尝试结果列表
        """
        attempts = []
        
        # 标准HTML实体解码
        attempt = self._try_decode_standard(data)
        attempts.append(attempt)
        
        # 手动解码常见实体
        manual_attempt = self._try_decode_manual(data)
        attempts.append(manual_attempt)
        
        return attempts
    
    def _try_decode_standard(self, data: str) -> DecryptionAttempt:
        """尝试标准HTML实体解码"""
        try:
            decoded_str = html.unescape(data)
            
            # 检查是否真的进行了解码
            if decoded_str == data:
                success = False
                confidence = 0.0
            else:
                success = self._is_printable_text(decoded_str)
                confidence = 0.8 if success else 0.4
            
            return self._create_attempt(
                algorithm="HTML Entities",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="HTML Entities",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _try_decode_manual(self, data: str) -> DecryptionAttempt:
        """尝试手动解码常见HTML实体"""
        try:
            decoded_str = data
            changes_made = False
            
            # 解码常见命名实体
            for entity, char in self.common_entities.items():
                if entity in decoded_str:
                    decoded_str = decoded_str.replace(entity, char)
                    changes_made = True
            
            # 解码数字实体
            # 十进制数字实体 &#数字;
            decimal_entities = re.findall(r'&#(\d+);', decoded_str)
            for num_str in decimal_entities:
                try:
                    char_code = int(num_str)
                    if 0 <= char_code <= 1114111:  # Unicode范围
                        char = chr(char_code)
                        decoded_str = decoded_str.replace(f'&#{num_str};', char)
                        changes_made = True
                except ValueError:
                    continue
            
            # 十六进制数字实体 &#x十六进制;
            hex_entities = re.findall(r'&#x([0-9A-Fa-f]+);', decoded_str)
            for hex_str in hex_entities:
                try:
                    char_code = int(hex_str, 16)
                    if 0 <= char_code <= 1114111:  # Unicode范围
                        char = chr(char_code)
                        decoded_str = decoded_str.replace(f'&#x{hex_str};', char)
                        changes_made = True
                except ValueError:
                    continue
            
            if changes_made:
                success = self._is_printable_text(decoded_str)
                confidence = 0.7 if success else 0.3
            else:
                success = False
                confidence = 0.0
            
            return self._create_attempt(
                algorithm="HTML Entities (manual)",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="HTML Entities (manual)",
                data=data,
                success=False,
                error=str(e)
            )
