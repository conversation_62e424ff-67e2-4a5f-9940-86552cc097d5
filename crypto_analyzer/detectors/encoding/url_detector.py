"""
URL编码检测器
=============

检测和解码URL编码的数据。
"""

import re
import urllib.parse
from typing import List
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class URLDetector(DetectorBase):
    """URL编码检测器"""
    
    def __init__(self):
        super().__init__("URL Encoding", "encoding")
        self.priority = 75
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是URL编码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        confidence = 0.0
        
        # 1. 检查是否包含%编码模式
        percent_encoded = re.findall(r'%[0-9A-Fa-f]{2}', data)
        if percent_encoded:
            # 计算编码字符的比例
            encoded_chars = len(percent_encoded) * 3  # 每个%XX占3个字符
            encoded_ratio = encoded_chars / len(data)
            confidence += min(encoded_ratio * 2, 0.6)  # 最多0.6分
        
        # 2. 检查是否包含常见的URL编码字符
        common_encoded = [
            '%20',  # 空格
            '%21',  # !
            '%22',  # "
            '%23',  # #
            '%24',  # $
            '%25',  # %
            '%26',  # &
            '%27',  # '
            '%28',  # (
            '%29',  # )
            '%2B',  # +
            '%2C',  # ,
            '%2F',  # /
            '%3A',  # :
            '%3B',  # ;
            '%3C',  # <
            '%3D',  # =
            '%3E',  # >
            '%3F',  # ?
            '%40',  # @
        ]
        
        common_count = sum(1 for pattern in common_encoded if pattern.lower() in data.lower())
        if common_count > 0:
            confidence += min(common_count * 0.1, 0.3)
        
        # 3. 检查+号（URL编码中+代表空格）
        if '+' in data and percent_encoded:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解码URL编码数据
        
        Args:
            data: 待解码的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解码尝试结果列表
        """
        attempts = []
        
        # 标准URL解码
        attempt = self._try_decode_standard(data)
        attempts.append(attempt)
        
        # 多次解码（处理多重编码）
        multi_attempt = self._try_decode_multiple(data)
        attempts.append(multi_attempt)
        
        return attempts
    
    def _try_decode_standard(self, data: str) -> DecryptionAttempt:
        """尝试标准URL解码"""
        try:
            decoded_str = urllib.parse.unquote(data)
            
            # 检查是否真的进行了解码
            if decoded_str == data:
                success = False
                confidence = 0.0
            else:
                success = self._is_printable_text(decoded_str)
                confidence = 0.8 if success else 0.4
            
            return self._create_attempt(
                algorithm="URL Encoding",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="URL Encoding",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _try_decode_multiple(self, data: str) -> DecryptionAttempt:
        """尝试多次URL解码"""
        try:
            current = data
            decode_count = 0
            max_iterations = 5  # 防止无限循环
            
            while decode_count < max_iterations:
                decoded = urllib.parse.unquote(current)
                if decoded == current:
                    break  # 没有更多变化
                current = decoded
                decode_count += 1
            
            if decode_count > 1:
                success = self._is_printable_text(current)
                confidence = 0.7 if success else 0.3
                
                return self._create_attempt(
                    algorithm=f"URL Encoding (x{decode_count})",
                    data=data,
                    result=current,
                    success=success,
                    confidence=confidence
                )
            else:
                return self._create_attempt(
                    algorithm="URL Encoding (multiple)",
                    data=data,
                    success=False,
                    error="No multiple encoding detected"
                )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="URL Encoding (multiple)",
                data=data,
                success=False,
                error=str(e)
            )
