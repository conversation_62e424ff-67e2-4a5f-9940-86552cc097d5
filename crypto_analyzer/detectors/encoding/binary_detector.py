"""
二进制编码检测器
================

检测和解码二进制编码的数据。
"""

import re
from typing import List
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class BinaryDetector(DetectorBase):
    """二进制编码检测器"""
    
    def __init__(self):
        super().__init__("Binary", "encoding")
        self.priority = 60
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是二进制编码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        # 移除空白字符
        clean_data = re.sub(r'\s', '', data)
        
        confidence = 0.0
        
        # 1. 检查是否只包含0和1
        if re.match(r'^[01]+$', clean_data):
            confidence += 0.5
        else:
            return 0.0
        
        # 2. 长度检查 - 应该是8的倍数（字节对齐）
        if len(clean_data) % 8 == 0:
            confidence += 0.3
        
        # 3. 长度合理性检查
        if 8 <= len(clean_data) <= 10000:
            confidence += 0.1
        
        # 4. 检查0和1的分布是否合理
        zero_count = clean_data.count('0')
        one_count = clean_data.count('1')
        if zero_count > 0 and one_count > 0:
            # 如果0和1的比例不是极端的，给予额外分数
            ratio = min(zero_count, one_count) / max(zero_count, one_count)
            if ratio > 0.1:  # 不是极端的全0或全1
                confidence += 0.1
        
        return min(confidence, 1.0)
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解码二进制数据
        
        Args:
            data: 待解码的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解码尝试结果列表
        """
        attempts = []
        
        # 移除空白字符
        clean_data = re.sub(r'\s', '', data)
        
        # 标准二进制解码（8位一组）
        attempt = self._try_decode_standard(clean_data)
        attempts.append(attempt)
        
        # 如果长度不是8的倍数，尝试添加前导0
        if len(clean_data) % 8 != 0:
            padded_attempt = self._try_decode_with_padding(clean_data)
            attempts.append(padded_attempt)
        
        return attempts
    
    def _try_decode_standard(self, data: str) -> DecryptionAttempt:
        """尝试标准二进制解码"""
        try:
            if len(data) % 8 != 0:
                raise ValueError("Binary string length must be a multiple of 8")
            
            # 将二进制字符串分成8位一组
            bytes_list = []
            for i in range(0, len(data), 8):
                byte_str = data[i:i+8]
                byte_value = int(byte_str, 2)
                bytes_list.append(byte_value)
            
            # 转换为字节串然后解码为字符串
            decoded_bytes = bytes(bytes_list)
            decoded_str = decoded_bytes.decode('utf-8', errors='ignore')
            
            success = self._is_printable_text(decoded_str)
            confidence = 0.8 if success else 0.3
            
            return self._create_attempt(
                algorithm="Binary",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="Binary",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _try_decode_with_padding(self, data: str) -> DecryptionAttempt:
        """尝试添加前导0后解码"""
        try:
            # 计算需要的前导0
            padding_needed = 8 - (len(data) % 8)
            padded_data = '0' * padding_needed + data
            
            # 将二进制字符串分成8位一组
            bytes_list = []
            for i in range(0, len(padded_data), 8):
                byte_str = padded_data[i:i+8]
                byte_value = int(byte_str, 2)
                bytes_list.append(byte_value)
            
            # 转换为字节串然后解码为字符串
            decoded_bytes = bytes(bytes_list)
            decoded_str = decoded_bytes.decode('utf-8', errors='ignore')
            
            success = self._is_printable_text(decoded_str)
            confidence = 0.7 if success else 0.2
            
            return self._create_attempt(
                algorithm="Binary (with padding)",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="Binary (with padding)",
                data=data,
                success=False,
                error=str(e)
            )
