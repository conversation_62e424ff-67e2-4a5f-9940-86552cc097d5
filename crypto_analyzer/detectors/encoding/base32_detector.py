"""
Base32编码检测器
================

检测和解码Base32编码的数据。
"""

import re
import base64
from typing import List
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class Base32Detector(DetectorBase):
    """Base32编码检测器"""
    
    def __init__(self):
        super().__init__("Base32", "encoding")
        self.priority = 85
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是Base32编码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        # 移除空白字符
        clean_data = re.sub(r'\s', '', data).upper()
        
        confidence = 0.0
        
        # 1. 字符集检查 - Base32只包含 A-Z, 2-7, =
        if re.match(r'^[A-Z2-7]+=*$', clean_data):
            confidence += 0.4
        else:
            return 0.0
        
        # 2. 长度检查 - Base32编码后的长度应该是8的倍数
        if len(clean_data) % 8 == 0:
            confidence += 0.3
        
        # 3. 填充检查
        padding_count = clean_data.count('=')
        if padding_count <= 6:  # Base32最多6个填充字符
            if padding_count == 0 or clean_data.endswith('=' * padding_count):
                confidence += 0.2
        
        # 4. 长度合理性检查
        if 8 <= len(clean_data) <= 10000:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解码Base32数据
        
        Args:
            data: 待解码的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解码尝试结果列表
        """
        attempts = []
        
        # 移除空白字符并转换为大写
        clean_data = re.sub(r'\s', '', data).upper()
        
        # 标准Base32解码
        attempt = self._try_decode_standard(clean_data)
        attempts.append(attempt)
        
        # 如果标准解码失败，尝试添加填充
        if not attempt.success:
            padded_attempt = self._try_decode_with_padding(clean_data)
            attempts.append(padded_attempt)
        
        return attempts
    
    def _try_decode_standard(self, data: str) -> DecryptionAttempt:
        """尝试标准Base32解码"""
        try:
            decoded_bytes = base64.b32decode(data)
            decoded_str = decoded_bytes.decode('utf-8', errors='ignore')
            
            success = self._is_printable_text(decoded_str)
            confidence = 0.8 if success else 0.3
            
            return self._create_attempt(
                algorithm="Base32",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="Base32",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _try_decode_with_padding(self, data: str) -> DecryptionAttempt:
        """尝试添加填充后解码"""
        try:
            # 计算需要的填充
            missing_padding = 8 - (len(data) % 8)
            if missing_padding != 8:
                padded_data = data + '=' * missing_padding
            else:
                padded_data = data
            
            decoded_bytes = base64.b32decode(padded_data)
            decoded_str = decoded_bytes.decode('utf-8', errors='ignore')
            
            success = self._is_printable_text(decoded_str)
            confidence = 0.7 if success else 0.2
            
            return self._create_attempt(
                algorithm="Base32 (with padding)",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="Base32 (with padding)",
                data=data,
                success=False,
                error=str(e)
            )
