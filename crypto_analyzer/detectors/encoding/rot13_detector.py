"""
ROT13编码检测器
===============

检测和解码ROT13编码的数据。
"""

import re
import string
from typing import List
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class ROT13Detector(DetectorBase):
    """ROT13编码检测器"""
    
    def __init__(self):
        super().__init__("ROT13", "encoding")
        self.priority = 65
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是ROT13编码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        # ROT13主要用于英文文本，检查是否主要包含字母
        letters = re.findall(r'[a-zA-Z]', data)
        if not letters:
            return 0.0
        
        letter_ratio = len(letters) / len(data)
        
        # 如果字母比例较高，可能是ROT13
        if letter_ratio > 0.5:
            return min(letter_ratio, 0.8)
        else:
            return 0.0
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解码ROT13数据
        
        Args:
            data: 待解码的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解码尝试结果列表
        """
        attempts = []
        
        # ROT13解码
        rot13_attempt = self._try_decode_rot13(data)
        attempts.append(rot13_attempt)
        
        # 尝试其他ROT变体
        for shift in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]:
            rot_attempt = self._try_decode_rot(data, shift)
            attempts.append(rot_attempt)
        
        return attempts
    
    def _try_decode_rot13(self, data: str) -> DecryptionAttempt:
        """尝试ROT13解码"""
        try:
            # 使用Python内置的ROT13编码器
            decoded_str = data.encode('rot13')
            
            success = self._is_printable_text(decoded_str)
            confidence = 0.8 if success else 0.3
            
            return self._create_attempt(
                algorithm="ROT13",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            # 如果内置方法失败，使用手动实现
            return self._try_decode_rot(data, 13)
    
    def _try_decode_rot(self, data: str, shift: int) -> DecryptionAttempt:
        """尝试指定偏移量的ROT解码"""
        try:
            decoded_chars = []
            
            for char in data:
                if char.isalpha():
                    # 确定是大写还是小写
                    if char.isupper():
                        # 大写字母
                        shifted = chr((ord(char) - ord('A') - shift) % 26 + ord('A'))
                    else:
                        # 小写字母
                        shifted = chr((ord(char) - ord('a') - shift) % 26 + ord('a'))
                    decoded_chars.append(shifted)
                else:
                    # 非字母字符保持不变
                    decoded_chars.append(char)
            
            decoded_str = ''.join(decoded_chars)
            
            # 检查解码结果的合理性
            success = self._is_printable_text(decoded_str)
            
            # 对于ROT13给予更高的置信度
            if shift == 13:
                confidence = 0.8 if success else 0.3
            else:
                confidence = 0.6 if success else 0.2
            
            algorithm_name = f"ROT{shift}" if shift != 13 else "ROT13"
            
            return self._create_attempt(
                algorithm=algorithm_name,
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            algorithm_name = f"ROT{shift}" if shift != 13 else "ROT13"
            return self._create_attempt(
                algorithm=algorithm_name,
                data=data,
                success=False,
                error=str(e)
            )
