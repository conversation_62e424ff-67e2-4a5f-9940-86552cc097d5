"""
Base16(Hex)编码检测器
====================

检测和解码Base16(十六进制)编码的数据。
"""

import re
import binascii
from typing import List
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class Base16Detector(DetectorBase):
    """Base16(Hex)编码检测器"""
    
    def __init__(self):
        super().__init__("Base16", "encoding")
        self.priority = 80
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是Base16编码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        # 移除空白字符
        clean_data = re.sub(r'\s', '', data)
        
        confidence = 0.0
        
        # 1. 字符集检查 - Hex只包含 0-9, A-F, a-f
        if re.match(r'^[0-9A-Fa-f]+$', clean_data):
            confidence += 0.5
        else:
            return 0.0
        
        # 2. 长度检查 - Hex编码后的长度应该是偶数
        if len(clean_data) % 2 == 0:
            confidence += 0.3
        
        # 3. 长度合理性检查
        if 2 <= len(clean_data) <= 10000:
            confidence += 0.1
        
        # 4. 特征模式检查
        # 如果包含较多的A-F字符，更可能是Hex
        hex_chars = sum(1 for c in clean_data.upper() if c in 'ABCDEF')
        hex_ratio = hex_chars / len(clean_data)
        if hex_ratio > 0.1:  # 至少10%的字符是A-F
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解码Base16数据
        
        Args:
            data: 待解码的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解码尝试结果列表
        """
        attempts = []
        
        # 移除空白字符
        clean_data = re.sub(r'\s', '', data)
        
        # 标准Hex解码
        attempt = self._try_decode_standard(clean_data)
        attempts.append(attempt)
        
        # 如果长度是奇数，尝试在前面添加0
        if len(clean_data) % 2 == 1:
            padded_attempt = self._try_decode_with_padding(clean_data)
            attempts.append(padded_attempt)
        
        return attempts
    
    def _try_decode_standard(self, data: str) -> DecryptionAttempt:
        """尝试标准Hex解码"""
        try:
            decoded_bytes = binascii.unhexlify(data)
            decoded_str = decoded_bytes.decode('utf-8', errors='ignore')
            
            success = self._is_printable_text(decoded_str)
            confidence = 0.8 if success else 0.3
            
            return self._create_attempt(
                algorithm="Base16/Hex",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="Base16/Hex",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _try_decode_with_padding(self, data: str) -> DecryptionAttempt:
        """尝试添加前导0后解码"""
        try:
            padded_data = '0' + data
            decoded_bytes = binascii.unhexlify(padded_data)
            decoded_str = decoded_bytes.decode('utf-8', errors='ignore')
            
            success = self._is_printable_text(decoded_str)
            confidence = 0.7 if success else 0.2
            
            return self._create_attempt(
                algorithm="Base16/Hex (with padding)",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="Base16/Hex (with padding)",
                data=data,
                success=False,
                error=str(e)
            )
