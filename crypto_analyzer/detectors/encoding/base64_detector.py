"""
Base64编码检测器
================

检测和解码Base64编码的数据。
"""

import re
import base64
from typing import List
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class Base64Detector(DetectorBase):
    """Base64编码检测器"""
    
    def __init__(self):
        super().__init__("Base64", "encoding")
        self.priority = 90
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是Base64编码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        # 移除空白字符
        clean_data = re.sub(r'\s', '', data)
        
        # 基本特征检查
        confidence = 0.0
        
        # 1. 字符集检查 - Base64只包含 A-Z, a-z, 0-9, +, /, =
        if re.match(r'^[A-Za-z0-9+/]+=*$', clean_data):
            confidence += 0.4
        else:
            return 0.0  # 如果字符集不匹配，直接返回0
        
        # 2. 长度检查 - Base64编码后的长度应该是4的倍数
        if len(clean_data) % 4 == 0:
            confidence += 0.3
        
        # 3. 填充检查 - 正确的填充模式
        padding_count = clean_data.count('=')
        if padding_count <= 2:  # 最多2个填充字符
            if padding_count == 0 or clean_data.endswith('=' * padding_count):
                confidence += 0.2
        
        # 4. 长度合理性检查
        if 4 <= len(clean_data) <= 10000:  # 合理的长度范围
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解码Base64数据
        
        Args:
            data: 待解码的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解码尝试结果列表
        """
        attempts = []
        
        # 移除空白字符
        clean_data = re.sub(r'\s', '', data)
        
        # 标准Base64解码
        attempt = self._try_decode_standard(clean_data)
        attempts.append(attempt)
        
        # 如果标准解码失败，尝试添加填充
        if not attempt.success:
            padded_attempt = self._try_decode_with_padding(clean_data)
            attempts.append(padded_attempt)
        
        # 尝试URL安全的Base64解码
        url_safe_attempt = self._try_decode_url_safe(clean_data)
        attempts.append(url_safe_attempt)
        
        return attempts
    
    def _try_decode_standard(self, data: str) -> DecryptionAttempt:
        """尝试标准Base64解码"""
        try:
            decoded_bytes = base64.b64decode(data, validate=True)
            decoded_str = decoded_bytes.decode('utf-8', errors='ignore')
            
            # 检查解码结果的合理性
            success = self._is_printable_text(decoded_str)
            confidence = 0.8 if success else 0.3
            
            return self._create_attempt(
                algorithm="Base64",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="Base64",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _try_decode_with_padding(self, data: str) -> DecryptionAttempt:
        """尝试添加填充后解码"""
        try:
            # 计算需要的填充
            missing_padding = 4 - (len(data) % 4)
            if missing_padding != 4:
                padded_data = data + '=' * missing_padding
            else:
                padded_data = data
            
            decoded_bytes = base64.b64decode(padded_data, validate=True)
            decoded_str = decoded_bytes.decode('utf-8', errors='ignore')
            
            success = self._is_printable_text(decoded_str)
            confidence = 0.7 if success else 0.2
            
            return self._create_attempt(
                algorithm="Base64 (with padding)",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="Base64 (with padding)",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _try_decode_url_safe(self, data: str) -> DecryptionAttempt:
        """尝试URL安全的Base64解码"""
        try:
            # 将URL安全字符替换为标准Base64字符
            url_safe_data = data.replace('-', '+').replace('_', '/')
            
            # 添加填充如果需要
            missing_padding = 4 - (len(url_safe_data) % 4)
            if missing_padding != 4:
                url_safe_data += '=' * missing_padding
            
            decoded_bytes = base64.b64decode(url_safe_data, validate=True)
            decoded_str = decoded_bytes.decode('utf-8', errors='ignore')
            
            success = self._is_printable_text(decoded_str)
            confidence = 0.6 if success else 0.2
            
            return self._create_attempt(
                algorithm="Base64 URL-Safe",
                data=data,
                result=decoded_str,
                success=success,
                confidence=confidence
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="Base64 URL-Safe",
                data=data,
                success=False,
                error=str(e)
            )
