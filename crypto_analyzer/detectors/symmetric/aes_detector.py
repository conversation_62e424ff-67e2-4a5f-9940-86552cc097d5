"""
AES加密检测器
=============

检测AES加密的数据。由于AES是对称加密，需要密钥才能解密，
此检测器主要用于识别可能的AES密文特征。
"""

import re
import base64
import binascii
from typing import List
from ...core.detector_base import DetectorBase
from ...core.result import DecryptionAttempt


class AESDetector(DetectorBase):
    """AES加密检测器"""
    
    def __init__(self):
        super().__init__("AES", "symmetric")
        self.priority = 85
    
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能是AES加密
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        if not data:
            return 0.0
        
        confidence = 0.0
        
        # 移除空白字符
        clean_data = re.sub(r'\s', '', data)
        
        # 1. 检查是否是Base64编码的二进制数据
        if re.match(r'^[A-Za-z0-9+/]+=*$', clean_data):
            try:
                decoded = base64.b64decode(clean_data)
                # AES块大小是16字节，检查长度是否是16的倍数
                if len(decoded) % 16 == 0 and len(decoded) >= 16:
                    confidence += 0.4
                    
                    # 检查数据的随机性（AES密文应该看起来很随机）
                    if self._check_randomness(decoded):
                        confidence += 0.2
                        
            except Exception:
                pass
        
        # 2. 检查是否是十六进制编码的二进制数据
        elif re.match(r'^[0-9A-Fa-f]+$', clean_data) and len(clean_data) % 2 == 0:
            try:
                decoded = binascii.unhexlify(clean_data)
                if len(decoded) % 16 == 0 and len(decoded) >= 16:
                    confidence += 0.3
                    
                    if self._check_randomness(decoded):
                        confidence += 0.2
                        
            except Exception:
                pass
        
        # 3. 长度特征检查
        if len(clean_data) >= 32:  # 至少一个AES块的Base64编码长度
            confidence += 0.1
        
        return min(confidence, 0.8)  # AES检测置信度不会太高，因为需要密钥验证
    
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试识别AES加密数据（无法在没有密钥的情况下解密）
        
        Args:
            data: 待识别的数据
            **kwargs: 额外参数，可能包含密钥
            
        Returns:
            List[DecryptionAttempt]: 识别结果列表
        """
        attempts = []
        
        # 移除空白字符
        clean_data = re.sub(r'\s', '', data)
        
        # 识别可能的AES密文
        attempt = self._identify_aes(clean_data)
        attempts.append(attempt)
        
        # 如果提供了密钥，尝试解密
        key = kwargs.get('key')
        if key:
            decrypt_attempt = self._try_decrypt_with_key(clean_data, key)
            attempts.append(decrypt_attempt)
        
        return attempts
    
    def _check_randomness(self, data: bytes) -> bool:
        """检查数据的随机性"""
        if len(data) < 16:
            return False
        
        # 简单的随机性检查：计算字节值的分布
        byte_counts = [0] * 256
        for byte in data:
            byte_counts[byte] += 1
        
        # 计算卡方统计量
        expected = len(data) / 256
        chi_squared = sum((count - expected) ** 2 / expected for count in byte_counts if expected > 0)
        
        # 如果卡方值在合理范围内，认为数据是随机的
        # 这是一个简化的检查，真实的随机性测试会更复杂
        return 200 <= chi_squared <= 400
    
    def _identify_aes(self, data: str) -> DecryptionAttempt:
        """识别可能的AES密文"""
        try:
            # 尝试Base64解码
            if re.match(r'^[A-Za-z0-9+/]+=*$', data):
                try:
                    decoded = base64.b64decode(data)
                    if len(decoded) % 16 == 0 and len(decoded) >= 16:
                        return self._create_attempt(
                            algorithm="AES (Base64)",
                            data=data,
                            result=f"Possible AES ciphertext (Base64 encoded, {len(decoded)} bytes)",
                            success=True,
                            confidence=0.6
                        )
                except Exception:
                    pass
            
            # 尝试十六进制解码
            elif re.match(r'^[0-9A-Fa-f]+$', data) and len(data) % 2 == 0:
                try:
                    decoded = binascii.unhexlify(data)
                    if len(decoded) % 16 == 0 and len(decoded) >= 16:
                        return self._create_attempt(
                            algorithm="AES (Hex)",
                            data=data,
                            result=f"Possible AES ciphertext (Hex encoded, {len(decoded)} bytes)",
                            success=True,
                            confidence=0.6
                        )
                except Exception:
                    pass
            
            return self._create_attempt(
                algorithm="AES",
                data=data,
                success=False,
                error="Data format not consistent with AES ciphertext"
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="AES",
                data=data,
                success=False,
                error=str(e)
            )
    
    def _try_decrypt_with_key(self, data: str, key: str) -> DecryptionAttempt:
        """尝试使用提供的密钥解密AES"""
        try:
            from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
            from cryptography.hazmat.backends import default_backend
            
            # 解码密文
            if re.match(r'^[A-Za-z0-9+/]+=*$', data):
                ciphertext = base64.b64decode(data)
            elif re.match(r'^[0-9A-Fa-f]+$', data):
                ciphertext = binascii.unhexlify(data)
            else:
                raise ValueError("Unsupported data format")
            
            # 准备密钥
            if isinstance(key, str):
                if re.match(r'^[0-9A-Fa-f]+$', key):
                    key_bytes = binascii.unhexlify(key)
                else:
                    key_bytes = key.encode('utf-8')
            else:
                key_bytes = key
            
            # 确保密钥长度正确（16, 24, 或 32 字节）
            if len(key_bytes) not in [16, 24, 32]:
                # 尝试调整密钥长度
                if len(key_bytes) < 16:
                    key_bytes = key_bytes.ljust(16, b'\x00')
                elif len(key_bytes) < 24:
                    key_bytes = key_bytes[:16]
                elif len(key_bytes) < 32:
                    key_bytes = key_bytes[:24]
                else:
                    key_bytes = key_bytes[:32]
            
            # 尝试不同的AES模式
            modes_to_try = [
                ("ECB", modes.ECB()),
                ("CBC", modes.CBC(b'\x00' * 16)),  # 零IV，实际应用中不安全
            ]
            
            for mode_name, mode in modes_to_try:
                try:
                    cipher = Cipher(algorithms.AES(key_bytes), mode, backend=default_backend())
                    decryptor = cipher.decryptor()
                    decrypted = decryptor.update(ciphertext) + decryptor.finalize()
                    
                    # 尝试解码为文本
                    try:
                        decrypted_text = decrypted.decode('utf-8', errors='ignore')
                        if self._is_printable_text(decrypted_text):
                            return self._create_attempt(
                                algorithm=f"AES-{mode_name}",
                                data=data,
                                result=decrypted_text,
                                success=True,
                                confidence=0.9,
                                key=key,
                                mode=mode_name
                            )
                    except UnicodeDecodeError:
                        pass
                        
                except Exception:
                    continue
            
            return self._create_attempt(
                algorithm="AES (with key)",
                data=data,
                success=False,
                error="Decryption failed with provided key",
                key=key
            )
            
        except Exception as e:
            return self._create_attempt(
                algorithm="AES (with key)",
                data=data,
                success=False,
                error=str(e),
                key=key
            )
