"""
核心模块
========

包含密码分析框架的核心组件：
- 主分析器
- 检测器基类
- 插件管理器
- 置信度评分器
- 结果数据结构
"""

from .analyzer import CryptoAnalyzer
from .detector_base import DetectorBase
from .plugin_manager import PluginManager
from .confidence_scorer import ConfidenceScorer
from .result import AnalysisResult

__all__ = [
    "CryptoAnalyzer",
    "DetectorBase",
    "PluginManager", 
    "ConfidenceScorer",
    "AnalysisResult",
]
