"""
置信度评分系统
==============

实现多维度的置信度评分算法，用于评估解密结果的可信度。
"""

import re
import math
from typing import Dict, Any, List, Optional
from collections import Counter


class ConfidenceScorer:
    """置信度评分器"""
    
    def __init__(self):
        """初始化评分器"""
        # 常见英文单词列表（简化版）
        self.common_words = {
            'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have',
            'i', 'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you',
            'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they',
            'we', 'say', 'her', 'she', 'or', 'an', 'will', 'my',
            'one', 'all', 'would', 'there', 'their', 'what', 'so',
            'up', 'out', 'if', 'about', 'who', 'get', 'which', 'go',
            'me', 'when', 'make', 'can', 'like', 'time', 'no', 'just',
            'him', 'know', 'take', 'people', 'into', 'year', 'your',
            'good', 'some', 'could', 'them', 'see', 'other', 'than',
            'then', 'now', 'look', 'only', 'come', 'its', 'over',
            'think', 'also', 'back', 'after', 'use', 'two', 'how',
            'our', 'work', 'first', 'well', 'way', 'even', 'new',
            'want', 'because', 'any', 'these', 'give', 'day', 'most', 'us'
        }
        
        # 英文字母频率（标准分布）
        self.english_freq = {
            'e': 12.7, 't': 9.1, 'a': 8.2, 'o': 7.5, 'i': 7.0, 'n': 6.7,
            's': 6.3, 'h': 6.1, 'r': 6.0, 'd': 4.3, 'l': 4.0, 'c': 2.8,
            'u': 2.8, 'm': 2.4, 'w': 2.4, 'f': 2.2, 'g': 2.0, 'y': 2.0,
            'p': 1.9, 'b': 1.3, 'v': 1.0, 'k': 0.8, 'j': 0.15, 'x': 0.15,
            'q': 0.10, 'z': 0.07
        }
    
    def calculate_confidence(self, 
                           original_data: str,
                           decrypted_data: str,
                           algorithm: str,
                           detection_confidence: float = 0.0,
                           **kwargs) -> float:
        """
        计算综合置信度
        
        Args:
            original_data: 原始加密数据
            decrypted_data: 解密后的数据
            algorithm: 使用的算法
            detection_confidence: 算法检测置信度
            **kwargs: 额外参数
            
        Returns:
            float: 综合置信度 (0.0-1.0)
        """
        if not decrypted_data:
            return 0.0
        
        # 各项评分权重
        weights = {
            'algorithm_match': 0.25,      # 算法匹配度
            'readability': 0.35,          # 可读性
            'char_distribution': 0.20,    # 字符分布
            'semantic_analysis': 0.20     # 语义分析
        }
        
        scores = {}
        
        # 1. 算法匹配度评分
        scores['algorithm_match'] = detection_confidence
        
        # 2. 可读性评分
        scores['readability'] = self._score_readability(decrypted_data)
        
        # 3. 字符分布评分
        scores['char_distribution'] = self._score_char_distribution(decrypted_data)
        
        # 4. 语义分析评分
        scores['semantic_analysis'] = self._score_semantic_analysis(decrypted_data)
        
        # 计算加权平均
        total_score = sum(scores[key] * weights[key] for key in weights)
        
        # 应用算法特定的调整
        total_score = self._apply_algorithm_adjustments(
            total_score, algorithm, original_data, decrypted_data
        )
        
        return max(0.0, min(1.0, total_score))
    
    def _score_readability(self, text: str) -> float:
        """评估文本可读性"""
        if not text:
            return 0.0
        
        score = 0.0
        
        # 可打印字符比例
        printable_chars = sum(1 for c in text if c.isprintable())
        printable_ratio = printable_chars / len(text)
        score += printable_ratio * 0.4
        
        # ASCII字符比例
        ascii_chars = sum(1 for c in text if ord(c) < 128)
        ascii_ratio = ascii_chars / len(text)
        score += ascii_ratio * 0.3
        
        # 字母数字比例
        alnum_chars = sum(1 for c in text if c.isalnum() or c.isspace())
        alnum_ratio = alnum_chars / len(text)
        score += alnum_ratio * 0.3
        
        return score
    
    def _score_char_distribution(self, text: str) -> float:
        """评估字符分布的合理性"""
        if not text:
            return 0.0
        
        # 转换为小写并只考虑字母
        letters = ''.join(c.lower() for c in text if c.isalpha())
        if not letters:
            return 0.5  # 如果没有字母，给中等分数
        
        # 计算字母频率
        letter_count = Counter(letters)
        total_letters = len(letters)
        
        # 计算与英文标准频率的相似度
        chi_squared = 0.0
        for letter in 'abcdefghijklmnopqrstuvwxyz':
            observed = (letter_count.get(letter, 0) / total_letters) * 100
            expected = self.english_freq.get(letter, 0.01)
            chi_squared += ((observed - expected) ** 2) / expected
        
        # 将卡方值转换为0-1的分数
        # 较小的卡方值表示更接近英文分布
        max_chi_squared = 1000  # 经验值
        similarity_score = max(0, 1 - (chi_squared / max_chi_squared))
        
        return similarity_score
    
    def _score_semantic_analysis(self, text: str) -> float:
        """评估文本的语义合理性"""
        if not text:
            return 0.0
        
        score = 0.0
        
        # 单词识别
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        if not words:
            return 0.1
        
        # 常见单词比例
        common_word_count = sum(1 for word in words if word in self.common_words)
        common_word_ratio = common_word_count / len(words)
        score += common_word_ratio * 0.5
        
        # 平均单词长度（英文单词平均长度约为4-5个字母）
        avg_word_length = sum(len(word) for word in words) / len(words)
        length_score = 1.0 - abs(avg_word_length - 4.5) / 10
        score += max(0, length_score) * 0.3
        
        # 检查是否包含中文字符
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', text))
        if has_chinese:
            score += 0.2
        
        return score
    
    def _apply_algorithm_adjustments(self, 
                                   base_score: float,
                                   algorithm: str,
                                   original_data: str,
                                   decrypted_data: str) -> float:
        """根据算法类型应用特定调整"""
        
        # Base64等编码算法的调整
        if algorithm.lower() in ['base64', 'base32', 'base16']:
            # 如果原始数据符合该编码的特征，提高置信度
            if algorithm.lower() == 'base64' and re.match(r'^[A-Za-z0-9+/]+=*$', original_data):
                base_score += 0.1
            elif algorithm.lower() == 'base32' and re.match(r'^[A-Z2-7]+=*$', original_data):
                base_score += 0.1
            elif algorithm.lower() == 'base16' and re.match(r'^[0-9A-Fa-f]+$', original_data):
                base_score += 0.1
        
        # 哈希算法的调整
        elif 'hash' in algorithm.lower() or algorithm.lower() in ['md5', 'sha1', 'sha256', 'sha512']:
            # 哈希算法通常不能"解密"，只能识别
            if len(decrypted_data) == len(original_data):
                base_score = 0.9  # 高置信度表示成功识别了哈希
        
        # 古典密码的调整
        elif algorithm.lower() in ['caesar', 'vigenere', 'rail_fence']:
            # 古典密码解密后应该是可读文本
            if self._is_readable_text(decrypted_data):
                base_score += 0.1
        
        return base_score
    
    def _is_readable_text(self, text: str) -> bool:
        """判断文本是否可读"""
        if not text:
            return False
        
        # 检查可打印字符比例
        printable_ratio = sum(1 for c in text if c.isprintable()) / len(text)
        
        # 检查是否包含单词
        words = re.findall(r'\b[a-zA-Z]+\b', text)
        has_words = len(words) > 0
        
        # 检查是否包含中文
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', text))
        
        return printable_ratio > 0.8 and (has_words or has_chinese)
