"""
插件管理器
==========

管理所有算法检测器插件的注册、加载和执行。
"""

import importlib
import pkgutil
from typing import Dict, List, Type, Optional
from loguru import logger

from .detector_base import DetectorBase


class PluginManager:
    """插件管理器"""
    
    def __init__(self):
        """初始化插件管理器"""
        self._detectors: Dict[str, DetectorBase] = {}
        self._detector_classes: Dict[str, Type[DetectorBase]] = {}
        self._categories: Dict[str, List[str]] = {
            'encoding': [],
            'symmetric': [],
            'asymmetric': [],
            'hash': [],
            'classical': [],
            'other': []
        }
    
    def register_detector(self, detector_class: Type[DetectorBase], 
                         name: Optional[str] = None) -> None:
        """
        注册检测器类
        
        Args:
            detector_class: 检测器类
            name: 检测器名称，如果为None则使用类名
        """
        if name is None:
            name = detector_class.__name__
        
        if not issubclass(detector_class, DetectorBase):
            raise ValueError(f"检测器类 {detector_class} 必须继承自 DetectorBase")
        
        self._detector_classes[name] = detector_class
        logger.info(f"已注册检测器类: {name}")
    
    def load_detector(self, name: str, **kwargs) -> DetectorBase:
        """
        加载并实例化检测器
        
        Args:
            name: 检测器名称
            **kwargs: 传递给检测器构造函数的参数
            
        Returns:
            DetectorBase: 检测器实例
        """
        if name not in self._detector_classes:
            raise ValueError(f"未找到检测器类: {name}")
        
        detector_class = self._detector_classes[name]
        detector = detector_class(**kwargs)
        
        self._detectors[name] = detector
        
        # 按类型分类
        category = getattr(detector, 'algorithm_type', 'other')
        if category in self._categories:
            if name not in self._categories[category]:
                self._categories[category].append(name)
        
        logger.info(f"已加载检测器: {name} (类型: {category})")
        return detector
    
    def get_detector(self, name: str) -> Optional[DetectorBase]:
        """
        获取检测器实例
        
        Args:
            name: 检测器名称
            
        Returns:
            Optional[DetectorBase]: 检测器实例，如果不存在则返回None
        """
        return self._detectors.get(name)
    
    def get_detectors_by_type(self, algorithm_type: str) -> List[DetectorBase]:
        """
        按类型获取检测器列表
        
        Args:
            algorithm_type: 算法类型
            
        Returns:
            List[DetectorBase]: 检测器列表
        """
        if algorithm_type not in self._categories:
            return []
        
        detectors = []
        for name in self._categories[algorithm_type]:
            detector = self._detectors.get(name)
            if detector and detector.enabled:
                detectors.append(detector)
        
        # 按优先级排序
        detectors.sort(key=lambda x: x.priority, reverse=True)
        return detectors
    
    def get_all_detectors(self) -> List[DetectorBase]:
        """
        获取所有启用的检测器
        
        Returns:
            List[DetectorBase]: 所有启用的检测器列表
        """
        detectors = [d for d in self._detectors.values() if d.enabled]
        detectors.sort(key=lambda x: x.priority, reverse=True)
        return detectors
    
    def enable_detector(self, name: str) -> bool:
        """
        启用检测器
        
        Args:
            name: 检测器名称
            
        Returns:
            bool: 是否成功启用
        """
        detector = self._detectors.get(name)
        if detector:
            detector.enabled = True
            logger.info(f"已启用检测器: {name}")
            return True
        return False
    
    def disable_detector(self, name: str) -> bool:
        """
        禁用检测器
        
        Args:
            name: 检测器名称
            
        Returns:
            bool: 是否成功禁用
        """
        detector = self._detectors.get(name)
        if detector:
            detector.enabled = False
            logger.info(f"已禁用检测器: {name}")
            return True
        return False
    
    def set_detector_priority(self, name: str, priority: int) -> bool:
        """
        设置检测器优先级
        
        Args:
            name: 检测器名称
            priority: 优先级（数值越高优先级越高）
            
        Returns:
            bool: 是否成功设置
        """
        detector = self._detectors.get(name)
        if detector:
            detector.priority = priority
            logger.info(f"已设置检测器 {name} 优先级为: {priority}")
            return True
        return False
    
    def auto_discover_detectors(self, package_name: str = "crypto_analyzer.detectors") -> int:
        """
        自动发现并注册检测器

        Args:
            package_name: 检测器包名

        Returns:
            int: 发现的检测器数量
        """
        discovered_count = 0

        try:
            # 导入检测器包
            package = importlib.import_module(package_name)

            # 遍历包中的所有模块和子包
            for importer, modname, ispkg in pkgutil.iter_modules(package.__path__):
                try:
                    if ispkg:
                        # 处理子包
                        subpackage_name = f"{package_name}.{modname}"
                        subpackage = importlib.import_module(subpackage_name)

                        # 遍历子包中的模块
                        for sub_importer, sub_modname, sub_ispkg in pkgutil.iter_modules(subpackage.__path__):
                            if not sub_ispkg:
                                try:
                                    module_name = f"{subpackage_name}.{sub_modname}"
                                    module = importlib.import_module(module_name)

                                    # 查找检测器类
                                    for attr_name in dir(module):
                                        attr = getattr(module, attr_name)
                                        if (isinstance(attr, type) and
                                            issubclass(attr, DetectorBase) and
                                            attr != DetectorBase):

                                            self.register_detector(attr)
                                            discovered_count += 1

                                except Exception as e:
                                    logger.warning(f"加载模块 {module_name} 时出错: {e}")
                    else:
                        # 处理直接模块
                        module_name = f"{package_name}.{modname}"
                        module = importlib.import_module(module_name)

                        # 查找检测器类
                        for attr_name in dir(module):
                            attr = getattr(module, attr_name)
                            if (isinstance(attr, type) and
                                issubclass(attr, DetectorBase) and
                                attr != DetectorBase):

                                self.register_detector(attr)
                                discovered_count += 1

                except Exception as e:
                    logger.warning(f"加载模块/包 {modname} 时出错: {e}")

        except ImportError as e:
            logger.warning(f"无法导入检测器包 {package_name}: {e}")

        logger.info(f"自动发现了 {discovered_count} 个检测器")
        return discovered_count
    
    def get_status(self) -> Dict[str, any]:
        """
        获取插件管理器状态
        
        Returns:
            Dict[str, any]: 状态信息
        """
        return {
            'total_detectors': len(self._detectors),
            'enabled_detectors': len([d for d in self._detectors.values() if d.enabled]),
            'categories': {k: len(v) for k, v in self._categories.items()},
            'detector_list': list(self._detectors.keys())
        }
