"""
主分析器
========

密码分析框架的核心分析器，协调各个检测器进行智能分析。
"""

import time
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger

from .plugin_manager import PluginManager
from .confidence_scorer import ConfidenceScorer
from .result import AnalysisResult, DecryptionAttempt
from .detector_base import DetectorBase


class CryptoAnalyzer:
    """主密码分析器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化分析器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.plugin_manager = PluginManager()
        self.confidence_scorer = ConfidenceScorer()
        
        # 配置参数
        self.max_workers = self.config.get('max_workers', 4)
        self.timeout = self.config.get('timeout', 30)
        self.min_confidence = self.config.get('min_confidence', 0.1)
        self.max_attempts = self.config.get('max_attempts', 50)
        
        # 自动发现并加载检测器
        self._initialize_detectors()
    
    def _initialize_detectors(self) -> None:
        """初始化检测器"""
        try:
            # 自动发现检测器
            count = self.plugin_manager.auto_discover_detectors()
            logger.info(f"自动发现了 {count} 个检测器类")
            
            # 加载所有发现的检测器
            for detector_name in self.plugin_manager._detector_classes.keys():
                try:
                    detector_class = self.plugin_manager._detector_classes[detector_name]
                    # 实例化检测器
                    detector_instance = detector_class()
                    # 手动添加到检测器字典中
                    self.plugin_manager._detectors[detector_name] = detector_instance

                    # 按类型分类
                    category = getattr(detector_instance, 'algorithm_type', 'other')
                    if category in self.plugin_manager._categories:
                        if detector_name not in self.plugin_manager._categories[category]:
                            self.plugin_manager._categories[category].append(detector_name)

                    logger.info(f"已加载检测器: {detector_name} (类型: {category})")
                except Exception as e:
                    logger.warning(f"加载检测器 {detector_name} 失败: {e}")
                    
        except Exception as e:
            logger.error(f"初始化检测器时出错: {e}")
    
    def analyze(self, data: str, **kwargs) -> AnalysisResult:
        """
        分析加密数据
        
        Args:
            data: 待分析的加密数据
            **kwargs: 额外参数
            
        Returns:
            AnalysisResult: 分析结果
        """
        start_time = time.time()
        
        # 创建结果对象
        result = AnalysisResult(input_data=data)
        
        try:
            # 第一阶段：检测可能的算法
            logger.info(f"开始分析数据: {data[:50]}...")
            detected_algorithms = self._detect_algorithms(data)
            result.detected_algorithms = detected_algorithms
            
            if not detected_algorithms:
                logger.warning("未检测到任何可能的算法")
                result.analysis_time = time.time() - start_time
                return result
            
            logger.info(f"检测到可能的算法: {detected_algorithms}")
            
            # 第二阶段：尝试解密
            attempts = self._attempt_decryption(data, detected_algorithms, **kwargs)

            # 添加所有尝试结果
            for attempt in attempts:
                result.add_attempt(attempt)
            
            # 记录分析时间
            result.analysis_time = time.time() - start_time
            
            logger.info(f"分析完成，共尝试 {len(attempts)} 次，"
                       f"成功 {len(result.get_successful_attempts())} 次")
            
        except Exception as e:
            logger.error(f"分析过程中出错: {e}")
            result.metadata['error'] = str(e)
            result.analysis_time = time.time() - start_time
        
        return result
    
    def _detect_algorithms(self, data: str) -> List[str]:
        """
        检测可能的算法
        
        Args:
            data: 待检测的数据
            
        Returns:
            List[str]: 可能的算法列表，按置信度排序
        """
        detectors = self.plugin_manager.get_all_detectors()
        algorithm_scores = []
        
        for detector in detectors:
            try:
                confidence = detector.can_detect(data)
                if confidence > self.min_confidence:
                    algorithm_scores.append((detector.name, confidence))
            except Exception as e:
                logger.warning(f"检测器 {detector.name} 检测时出错: {e}")
        
        # 按置信度排序
        algorithm_scores.sort(key=lambda x: x[1], reverse=True)
        
        # 返回算法名称列表
        return [name for name, score in algorithm_scores]
    
    def _attempt_decryption(self, data: str, algorithms: List[str], 
                          **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解密
        
        Args:
            data: 待解密的数据
            algorithms: 要尝试的算法列表
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解密尝试结果列表
        """
        all_attempts = []
        
        # 限制尝试次数
        algorithms = algorithms[:self.max_attempts]
        
        if self.max_workers > 1:
            # 并发执行
            all_attempts = self._parallel_decrypt(data, algorithms, **kwargs)
        else:
            # 串行执行
            all_attempts = self._serial_decrypt(data, algorithms, **kwargs)
        
        # 计算置信度并排序
        for attempt in all_attempts:
            if attempt.success and attempt.result:
                attempt.confidence = self.confidence_scorer.calculate_confidence(
                    original_data=data,
                    decrypted_data=attempt.result,
                    algorithm=attempt.algorithm,
                    detection_confidence=attempt.confidence
                )
        
        # 按置信度排序
        all_attempts.sort(key=lambda x: x.confidence, reverse=True)
        
        return all_attempts
    
    def _parallel_decrypt(self, data: str, algorithms: List[str], 
                         **kwargs) -> List[DecryptionAttempt]:
        """并发解密"""
        all_attempts = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_algorithm = {}
            for algorithm in algorithms:
                # 尝试通过算法名称找到检测器
                detector = None
                for detector_name, detector_instance in self.plugin_manager._detectors.items():
                    if detector_instance.name == algorithm:
                        detector = detector_instance
                        break

                if detector:
                    future = executor.submit(detector.decrypt, data, **kwargs)
                    future_to_algorithm[future] = algorithm
            
            # 收集结果
            for future in as_completed(future_to_algorithm, timeout=self.timeout):
                algorithm = future_to_algorithm[future]
                try:
                    attempts = future.result()
                    all_attempts.extend(attempts)
                except Exception as e:
                    logger.warning(f"算法 {algorithm} 解密时出错: {e}")
                    # 创建失败的尝试记录
                    failed_attempt = DecryptionAttempt(
                        algorithm=algorithm,
                        success=False,
                        error_message=str(e)
                    )
                    all_attempts.append(failed_attempt)
        
        return all_attempts
    
    def _serial_decrypt(self, data: str, algorithms: List[str], 
                       **kwargs) -> List[DecryptionAttempt]:
        """串行解密"""
        all_attempts = []
        
        for algorithm in algorithms:
            # 尝试通过算法名称找到检测器
            detector = None
            for detector_name, detector_instance in self.plugin_manager._detectors.items():
                if detector_instance.name == algorithm:
                    detector = detector_instance
                    break

            if detector:
                try:
                    attempts = detector.decrypt(data, **kwargs)
                    all_attempts.extend(attempts)
                except Exception as e:
                    logger.warning(f"算法 {algorithm} 解密时出错: {e}")
                    # 创建失败的尝试记录
                    failed_attempt = DecryptionAttempt(
                        algorithm=algorithm,
                        success=False,
                        error_message=str(e)
                    )
                    all_attempts.append(failed_attempt)
        
        return all_attempts
    
    def get_supported_algorithms(self) -> Dict[str, List[str]]:
        """
        获取支持的算法列表
        
        Returns:
            Dict[str, List[str]]: 按类型分组的算法列表
        """
        result = {}
        for category in ['encoding', 'symmetric', 'asymmetric', 'hash', 'classical', 'other']:
            detectors = self.plugin_manager.get_detectors_by_type(category)
            result[category] = [d.name for d in detectors]
        return result
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取分析器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            'plugin_manager_status': self.plugin_manager.get_status(),
            'config': self.config,
            'supported_algorithms': self.get_supported_algorithms()
        }
