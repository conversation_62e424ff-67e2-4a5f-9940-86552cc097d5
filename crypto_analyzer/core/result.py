"""
分析结果数据结构
================

定义密码分析的结果数据结构和相关工具类。
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import json


@dataclass
class DecryptionAttempt:
    """单次解密尝试的结果"""
    algorithm: str
    mode: Optional[str] = None
    key: Optional[str] = None
    iv: Optional[str] = None
    result: Optional[str] = None
    success: bool = False
    confidence: float = 0.0
    error_message: Optional[str] = None
    execution_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "algorithm": self.algorithm,
            "mode": self.mode,
            "key": self.key,
            "iv": self.iv,
            "result": self.result,
            "success": self.success,
            "confidence": self.confidence,
            "error_message": self.error_message,
            "execution_time": self.execution_time
        }


@dataclass
class AnalysisResult:
    """完整的分析结果"""
    input_data: str
    input_type: str = "unknown"
    detected_algorithms: List[str] = field(default_factory=list)
    attempts: List[DecryptionAttempt] = field(default_factory=list)
    best_result: Optional[DecryptionAttempt] = None
    overall_confidence: float = 0.0
    analysis_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_attempt(self, attempt: DecryptionAttempt) -> None:
        """添加解密尝试结果"""
        self.attempts.append(attempt)
        
        # 更新最佳结果
        if attempt.success and (
            self.best_result is None or 
            attempt.confidence > self.best_result.confidence
        ):
            self.best_result = attempt
            self.overall_confidence = attempt.confidence
    
    def get_successful_attempts(self) -> List[DecryptionAttempt]:
        """获取所有成功的解密尝试"""
        return [attempt for attempt in self.attempts if attempt.success]
    
    def get_top_attempts(self, n: int = 5) -> List[DecryptionAttempt]:
        """获取置信度最高的N个尝试"""
        sorted_attempts = sorted(
            self.attempts, 
            key=lambda x: x.confidence, 
            reverse=True
        )
        return sorted_attempts[:n]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "input_data": self.input_data,
            "input_type": self.input_type,
            "detected_algorithms": self.detected_algorithms,
            "attempts": [attempt.to_dict() for attempt in self.attempts],
            "best_result": self.best_result.to_dict() if self.best_result else None,
            "overall_confidence": self.overall_confidence,
            "analysis_time": self.analysis_time,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata
        }
    
    def to_json(self, indent: int = 2) -> str:
        """转换为JSON格式"""
        return json.dumps(self.to_dict(), indent=indent, ensure_ascii=False)
    
    def summary(self) -> str:
        """生成简要摘要"""
        if self.best_result:
            return (
                f"最佳结果: {self.best_result.algorithm}"
                f" (置信度: {self.best_result.confidence:.2%})\n"
                f"解密结果: {self.best_result.result[:100]}..."
                if len(self.best_result.result or "") > 100
                else f"解密结果: {self.best_result.result}"
            )
        else:
            return f"未找到有效的解密结果。尝试了 {len(self.attempts)} 种算法。"
