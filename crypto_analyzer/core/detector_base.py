"""
检测器基类
==========

定义所有算法检测器的基础接口和通用功能。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import re
import time
from .result import DecryptionAttempt


class DetectorBase(ABC):
    """算法检测器基类"""
    
    def __init__(self, name: str, algorithm_type: str):
        """
        初始化检测器
        
        Args:
            name: 检测器名称
            algorithm_type: 算法类型 (encoding, symmetric, asymmetric, hash, classical)
        """
        self.name = name
        self.algorithm_type = algorithm_type
        self.enabled = True
        self.priority = 50  # 优先级，数值越高优先级越高
    
    @abstractmethod
    def can_detect(self, data: str) -> float:
        """
        检测数据是否可能使用此算法加密/编码
        
        Args:
            data: 待检测的数据
            
        Returns:
            float: 检测置信度 (0.0-1.0)
        """
        pass
    
    @abstractmethod
    def decrypt(self, data: str, **kwargs) -> List[DecryptionAttempt]:
        """
        尝试解密/解码数据
        
        Args:
            data: 待解密的数据
            **kwargs: 额外参数
            
        Returns:
            List[DecryptionAttempt]: 解密尝试结果列表
        """
        pass
    
    def analyze_string_features(self, data: str) -> Dict[str, Any]:
        """
        分析字符串特征
        
        Args:
            data: 待分析的字符串
            
        Returns:
            Dict[str, Any]: 特征字典
        """
        if not data:
            return {}
        
        # 基础统计
        length = len(data)
        unique_chars = len(set(data))
        
        # 字符集分析
        has_uppercase = bool(re.search(r'[A-Z]', data))
        has_lowercase = bool(re.search(r'[a-z]', data))
        has_digits = bool(re.search(r'[0-9]', data))
        has_special = bool(re.search(r'[^A-Za-z0-9]', data))
        
        # 字符频率
        char_freq = {}
        for char in data:
            char_freq[char] = char_freq.get(char, 0) + 1
        
        # 熵计算
        entropy = self._calculate_entropy(data)
        
        # 模式检测
        has_padding = data.endswith('=') or data.endswith('==')
        is_hex_like = bool(re.match(r'^[0-9A-Fa-f]+$', data))
        is_base64_like = bool(re.match(r'^[A-Za-z0-9+/]+=*$', data))
        
        return {
            'length': length,
            'unique_chars': unique_chars,
            'char_diversity': unique_chars / length if length > 0 else 0,
            'has_uppercase': has_uppercase,
            'has_lowercase': has_lowercase,
            'has_digits': has_digits,
            'has_special': has_special,
            'entropy': entropy,
            'has_padding': has_padding,
            'is_hex_like': is_hex_like,
            'is_base64_like': is_base64_like,
            'char_frequency': char_freq
        }
    
    def _calculate_entropy(self, data: str) -> float:
        """计算字符串的信息熵"""
        if not data:
            return 0.0
        
        # 计算字符频率
        char_freq = {}
        for char in data:
            char_freq[char] = char_freq.get(char, 0) + 1
        
        # 计算熵
        entropy = 0.0
        length = len(data)
        for count in char_freq.values():
            probability = count / length
            if probability > 0:
                entropy -= probability * (probability.bit_length() - 1)
        
        return entropy
    
    def _is_printable_text(self, text: str) -> bool:
        """检查文本是否为可打印的有意义文本"""
        if not text:
            return False
        
        # 检查是否包含足够的可打印字符
        printable_count = sum(1 for c in text if c.isprintable())
        printable_ratio = printable_count / len(text)
        
        # 检查是否包含常见的英文单词或中文字符
        has_words = bool(re.search(r'\b[a-zA-Z]{2,}\b', text))
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', text))
        
        return printable_ratio > 0.8 and (has_words or has_chinese)
    
    def _create_attempt(self, algorithm: str, data: str, 
                       result: Optional[str] = None, 
                       success: bool = False,
                       confidence: float = 0.0,
                       error: Optional[str] = None,
                       **kwargs) -> DecryptionAttempt:
        """创建解密尝试结果"""
        return DecryptionAttempt(
            algorithm=algorithm,
            result=result,
            success=success,
            confidence=confidence,
            error_message=error,
            **kwargs
        )
    
    def _time_execution(self, func, *args, **kwargs) -> Tuple[Any, float]:
        """测量函数执行时间"""
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            return result, execution_time
        except Exception as e:
            execution_time = time.time() - start_time
            raise e
