#!/usr/bin/env python3
"""
最终解密分析
============

修复错误并进行最终的深度分析。
"""

import base64
import binascii
import math
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

def final_analysis():
    """最终解密分析"""
    
    # 原始加密串
    first_string = "yX67CAY2RrMSJ1TxneWBANxXBK5wL6Rvk2bDRa+DKUspUee9v69x1s3TH1rv8tP4wG6sJnnrJmZfMM7TEqmnisgTL6BdEeeLi7oyL6S3MwvEjGQlqs0iPesluloSgPHLd9fQCwbcvjzG10olmLTRox7llp7QHyQ1I4Jj76o4+Ldetv9hUFILGWA9t2d6cLdAlVuymsfY95N9rBQYn7gyHMzGp8h6Xx7dIPQzOUoq6F++fSQfNbp7EcW8WC6BGaKpUxBRQS1J5AG2G5BqhbnedXY4w7GogDKI1hALiTgcsoF8NHy7/3gwZ8o4AX6CvvXPmaefqio3cQZMiNcK9P0sU8l5NAwEuUhsnO4df+JrAV9vY0O3uw6zgrRDaDZvjm0elQKTpXQtWJzN8DolK51ddhELbxpZx7u6rF3SoX1DlPJOpXvoqe2zmV4YzD6O3QG9UQU4Nc4FmX8hNAEm8XRuavVaK6wWX17I4jRA5eG9zrNvl0SybAS6PbPGqCfp04uIcKhpFDPrALGzdy/yWi/2GKXSmGbrxbIFJ2v4DYPX0Gw="
    
    second_string = "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"
    
    print("🔍 最终解密分析")
    print("=" * 60)
    
    # 使用最有希望的密钥
    key = b"1234567890123456"
    
    print(f"\n🔑 使用密钥: {key.decode()}")
    
    # 分析第一个串
    print(f"\n📊 第一个加密串最终分析:")
    analyze_final(first_string, "第一个串", key)
    
    # 分析第二个串
    print(f"\n📊 第二个加密串最终分析:")
    analyze_final(second_string, "第二个串", key)

def analyze_final(encrypted_string, label, key):
    """最终分析单个加密串"""
    
    try:
        # Base64解码
        decoded_data = base64.b64decode(encrypted_string)
        print(f"Base64解码: {len(decoded_data)} 字节")
        
        # AES-ECB解密
        cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend())
        decryptor = cipher.decryptor()
        decrypted_ecb = decryptor.update(decoded_data) + decryptor.finalize()
        
        print(f"AES-ECB解密: {len(decrypted_ecb)} 字节")
        
        # 详细分析解密内容
        analyze_decrypted_detailed(decrypted_ecb, f"{label}-ECB")
        
        # AES-CBC解密
        cipher = Cipher(algorithms.AES(key), modes.CBC(b'\x00' * 16), backend=default_backend())
        decryptor = cipher.decryptor()
        decrypted_cbc = decryptor.update(decoded_data) + decryptor.finalize()
        
        print(f"AES-CBC解密: {len(decrypted_cbc)} 字节")
        
        # 详细分析解密内容
        analyze_decrypted_detailed(decrypted_cbc, f"{label}-CBC")
        
    except Exception as e:
        print(f"❌ 解密失败: {e}")

def analyze_decrypted_detailed(data, context):
    """详细分析解密后的内容"""
    
    print(f"\n🔬 详细分析 ({context}):")
    
    # 1. 尝试各种编码
    encodings = ['utf-8', 'gbk', 'gb2312', 'big5', 'latin-1', 'cp1252']
    
    for encoding in encodings:
        try:
            text = data.decode(encoding, errors='ignore')
            
            # 计算可打印字符比例
            printable_count = sum(1 for c in text if c.isprintable())
            printable_ratio = printable_count / len(text) if len(text) > 0 else 0
            
            # 计算ASCII字符比例
            ascii_count = sum(1 for c in text if ord(c) < 128)
            ascii_ratio = ascii_count / len(text) if len(text) > 0 else 0
            
            # 计算中文字符比例
            chinese_count = sum(1 for c in text if '\u4e00' <= c <= '\u9fff')
            chinese_ratio = chinese_count / len(text) if len(text) > 0 else 0
            
            print(f"\n📝 {encoding}编码分析:")
            print(f"  可打印字符: {printable_ratio:.2%}")
            print(f"  ASCII字符: {ascii_ratio:.2%}")
            print(f"  中文字符: {chinese_ratio:.2%}")
            
            if printable_ratio > 0.7:
                print(f"  ✅ 可能的有效文本")
                print(f"  内容预览: {text[:200]}...")
                
                # 检查特殊模式
                check_special_patterns(text)
                
                # 如果是高质量文本，尝试进一步分析
                if printable_ratio > 0.9 and ascii_ratio > 0.8:
                    print(f"  🎯 高质量ASCII文本，进行深度分析...")
                    deep_analyze_text(text)
                    
        except Exception as e:
            continue
    
    # 2. 二进制分析
    print(f"\n🔢 二进制分析:")
    print(f"  数据长度: {len(data)} 字节")
    print(f"  前32字节: {data[:32].hex()}")
    
    # 计算熵
    entropy = calculate_entropy(data)
    print(f"  数据熵: {entropy:.3f}")
    
    # 检查文件头
    check_file_headers(data)
    
    # 3. 尝试进一步解码
    print(f"\n🔄 尝试进一步解码:")
    try_further_decoding(data)

def check_special_patterns(text):
    """检查特殊模式"""
    
    # 检查Base64模式
    clean_text = ''.join(text.split())
    if len(clean_text) % 4 == 0 and len(clean_text) > 20:
        base64_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=')
        if all(c in base64_chars for c in clean_text):
            print(f"  🔍 可能包含Base64编码")
            try:
                nested_data = base64.b64decode(clean_text)
                print(f"  ✅ 嵌套Base64解码成功: {len(nested_data)} 字节")
                analyze_decrypted_detailed(nested_data, "嵌套Base64")
            except:
                print(f"  ❌ 嵌套Base64解码失败")
    
    # 检查十六进制模式
    hex_pattern = ''.join(c for c in text if c in '0123456789ABCDEFabcdef')
    if len(hex_pattern) > 40 and len(hex_pattern) % 2 == 0:
        try:
            hex_data = binascii.unhexlify(hex_pattern)
            print(f"  ✅ 可能包含十六进制数据: {len(hex_data)} 字节")
        except:
            pass

def deep_analyze_text(text):
    """深度分析文本内容"""
    
    # 检查是否包含常见关键词
    keywords = ['password', 'key', 'secret', 'token', 'flag', 'admin', 'user', 'login']
    found_keywords = [kw for kw in keywords if kw.lower() in text.lower()]
    if found_keywords:
        print(f"  🔍 发现关键词: {', '.join(found_keywords)}")
    
    # 检查URL模式
    import re
    urls = re.findall(r'https?://[^\s]+', text)
    if urls:
        print(f"  🔗 发现URL: {urls[:3]}")
    
    # 检查邮箱模式
    emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
    if emails:
        print(f"  📧 发现邮箱: {emails[:3]}")

def calculate_entropy(data):
    """计算数据熵"""
    if not data:
        return 0
    
    # 计算字节频率
    byte_counts = [0] * 256
    for byte in data:
        byte_counts[byte] += 1
    
    # 计算熵
    entropy = 0
    length = len(data)
    for count in byte_counts:
        if count > 0:
            probability = count / length
            entropy -= probability * math.log2(probability)
    
    return entropy

def check_file_headers(data):
    """检查文件头"""
    
    headers = {
        b'PK\x03\x04': 'ZIP文件',
        b'BM': 'BMP图像',
        b'\x89PNG': 'PNG图像',
        b'\xff\xd8\xff': 'JPEG图像',
        b'%PDF': 'PDF文件',
        b'GIF8': 'GIF图像',
        b'\x1f\x8b': 'GZIP压缩',
        b'BZh': 'BZIP2压缩',
        b'\x50\x4b': 'ZIP/Office文档',
    }
    
    for header, file_type in headers.items():
        if data.startswith(header):
            print(f"  🔍 检测到文件类型: {file_type}")
            return
    
    print(f"  ❌ 未识别出已知文件类型")

def try_further_decoding(data):
    """尝试进一步解码"""
    
    # 尝试作为Base64
    try:
        text = data.decode('ascii', errors='ignore')
        clean_text = ''.join(text.split())
        if len(clean_text) % 4 == 0 and len(clean_text) > 20:
            base64_data = base64.b64decode(clean_text)
            print(f"  ✅ 作为Base64解码: {len(base64_data)} 字节")
            # 递归分析
            if len(base64_data) < len(data):  # 避免无限递归
                analyze_decrypted_detailed(base64_data, "递归Base64")
    except:
        pass
    
    # 尝试作为十六进制
    try:
        text = data.decode('ascii', errors='ignore')
        hex_text = ''.join(c for c in text if c in '0123456789ABCDEFabcdef')
        if len(hex_text) % 2 == 0 and len(hex_text) > 20:
            hex_data = binascii.unhexlify(hex_text)
            print(f"  ✅ 作为十六进制解码: {len(hex_data)} 字节")
    except:
        pass

if __name__ == '__main__':
    final_analysis()
